'use client';
import React, { useState, useEffect } from 'react';
import { Box, Typography, Divider } from '@mui/material';
import CustomSwitch from '@/components/UI/Switch';
import CustomCheckbox from '@/components/UI/CustomCheckbox';
import ContactForm from './ContactForm';
import ContactCtaLinkForm from './ContactCtaLinkForm';
import CustomButton from '@/components/UI/CustomButton';
import * as Yup from 'yup';
import { staticOptions } from '@/helper/common/staticOptions';
import {
  getRecipeSettings,
  updateRecipeSettings,
} from '@/services/recipeService';
import { setApiMessage } from '@/helper/common/commonFunctions';
import './recipesettings.scss';

export default function RecipeSettings() {
  const [publicRecipeEnabled, setPublicRecipeEnabled] = useState(false);
  const [highlightDifferences, setHighlightDifferences] = useState(false);
  const [selectedCTA, setSelectedCTA] = useState('contact_form');
  const [loader, setLoader] = useState(false);

  // Validation states
  const [validationErrors, setValidationErrors] = useState({
    contactForm: {},
    ctaLinkForm: {},
  });
  const [selectedFields, setSelectedFields] = useState(() => {
    const defaultCheckedFields = [
      'category',
      'ingredients',
      'nutritional-information',
      'allergen-information',
      'preparation-steps',
    ];

    return staticOptions?.PUBLIC_DISPLAY_FIELDS?.reduce(
      (acc, field) => ({
        ...acc,
        [field?.slug]: defaultCheckedFields.includes(field?.slug)
          ? true
          : field?.isEnabled,
      }),
      {}
    );
  });

  // Contact form state
  const [contactFormData, setContactFormData] = useState({
    name: '',
    phone: '',
    email: '',
    link: '',
  });

  // CTA link form state
  const [ctaLinkFormData, setCtaLinkFormData] = useState({
    text: '',
    link: '',
  });

  useEffect(() => {
    const fetchRecipeSettings = async () => {
      try {
        const response = await getRecipeSettings();
        if (response?.data) {
          const {
            privateRecipeVisibilitySettings,
            publicRecipeSettings,
            publicRecipeCallToAction,
            recipeDetailsToDisplayPublicly,
          } = response.data;

          // Set private recipe visibility settings
          setHighlightDifferences(
            privateRecipeVisibilitySettings?.highlightChanges === 'true'
          );

          // Set public recipe settings
          setPublicRecipeEnabled(
            publicRecipeSettings?.publicStoreAccess === 'true'
          );

          // Set CTA settings
          if (publicRecipeCallToAction?.contactForm === 'true') {
            setSelectedCTA('contact_form');
          } else if (
            publicRecipeCallToAction?.customCtaLink?.enabled === 'true'
          ) {
            setSelectedCTA('custom_link');
            setCtaLinkFormData({
              text: publicRecipeCallToAction.customCtaLink.text || '',
              link: publicRecipeCallToAction.customCtaLink.link || '',
            });
          } else if (
            publicRecipeCallToAction?.contactInfo?.enabled === 'true'
          ) {
            setSelectedCTA('contact_info');
            setContactFormData({
              name: publicRecipeCallToAction.contactInfo.name || '',
              phone: publicRecipeCallToAction.contactInfo.phone || '',
              email: publicRecipeCallToAction.contactInfo.email || '',
              link: publicRecipeCallToAction.contactInfo.link || '',
            });
          } else {
            setSelectedCTA('none');
          }

          // Set public display fields
          const displayFields = {};
          if (recipeDetailsToDisplayPublicly) {
            const fieldMapping = {
              category: 'category',
              totalTime: 'total-time',
              yieldPortioning: 'yield-portioning',
              nutritionalInformation: 'nutritional-information',
              allergenInformation: 'allergen-information',
              dietarySuitability: 'dietary-suitability',
              cuisineType: 'cuisine-type',
              media: 'media',
              links: 'links',
              scale: 'scale',
              serveIn: 'serve-in',
              garnish: 'garnish',
              preparationSteps: 'preparation-steps',
              ingredients: 'ingredients',
              cost: 'cost',
            };

            Object.entries(recipeDetailsToDisplayPublicly).forEach(
              ([key, value]) => {
                const mappedKey = fieldMapping[key];
                if (mappedKey) {
                  displayFields[mappedKey] = value === 'true';
                }
              }
            );
          }
          setSelectedFields(displayFields);
        }
      } catch (error) {
        setApiMessage('error', error?.response?.data?.message);
      }
    };

    fetchRecipeSettings();
  }, []);

  const handleFieldToggle = (field) => {
    setSelectedFields((prev) => ({
      ...prev,
      [field]: !prev[field],
    }));
  };

  // Validation functions
  const validateContactForm = (data) => {
    const contactFormSchema = Yup.object().shape({
      name: Yup.string().trim().required('This field is required'),
      phone: Yup.string().trim().required('This field is required'),
      email: Yup.string().trim().required('This field is required'),
    });

    try {
      contactFormSchema.validateSync(data, { abortEarly: false });
      return {};
    } catch (error) {
      const errors = {};
      error.inner.forEach((err) => {
        errors[err.path] = err.message;
      });
      return errors;
    }
  };

  const validateCtaLinkForm = (data) => {
    const ctaLinkFormSchema = Yup.object().shape({
      text: Yup.string().trim().required('This field is required'),
      link: Yup.string()
        .trim()
        .required('This field is required')
        .url('Invalid URL'),
    });

    try {
      ctaLinkFormSchema.validateSync(data, { abortEarly: false });
      return {};
    } catch (error) {
      const errors = {};
      error.inner.forEach((err) => {
        errors[err.path] = err.message;
      });
      return errors;
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoader(true);

    try {
      let isValid = true;
      let newValidationErrors = {
        contactForm: {},
        ctaLinkForm: {},
      };

      // Validate contact form if selected
      if (selectedCTA === 'contact_info') {
        const contactErrors = validateContactForm(contactFormData);
        newValidationErrors.contactForm = contactErrors;
        if (Object.keys(contactErrors).length > 0) {
          isValid = false;
        }
      }

      // Validate CTA link form if selected
      if (selectedCTA === 'custom_link') {
        const ctaErrors = validateCtaLinkForm(ctaLinkFormData);
        newValidationErrors.ctaLinkForm = ctaErrors;
        if (Object.keys(ctaErrors).length > 0) {
          isValid = false;
        }
      }

      setValidationErrors(newValidationErrors);

      if (!isValid) {
        setLoader(false);
        return;
      }

      let formData = {
        privateRecipeVisibilitySettings: {
          highlightChanges: highlightDifferences ? 'true' : 'false',
        },
        publicRecipeSettings: {
          publicStoreAccess: publicRecipeEnabled ? 'true' : 'false',
        },
        publicRecipeCallToAction: {
          contactForm: selectedCTA === 'contact_form' ? 'true' : 'false',
          customCtaLink:
            selectedCTA === 'custom_link'
              ? {
                  enabled: 'true',
                  text: ctaLinkFormData.text?.trim(),
                  link: ctaLinkFormData.link?.trim(),
                }
              : { enabled: 'false' },
          contactInfo:
            selectedCTA === 'contact_info'
              ? {
                  enabled: 'true',
                  name: contactFormData.name?.trim(),
                  phone: contactFormData.phone?.trim(),
                  email: contactFormData.email?.trim(),
                  link: contactFormData.link?.trim(),
                }
              : { enabled: 'false' },
          none: selectedCTA === 'none' ? 'true' : 'false',
        },
        recipeDetailsToDisplayPublicly: Object.entries(selectedFields).reduce(
          (acc, [key, value]) => {
            // Convert kebab-case to camelCase
            const camelKey = key.replace(/-([a-z])/g, (g) =>
              g[1].toUpperCase()
            );
            return {
              ...acc,
              [camelKey]: value ? 'true' : 'false',
            };
          },
          {}
        ),
      };

      const response = await updateRecipeSettings(formData);
      if (response) {
        setApiMessage('success', response?.message);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    } finally {
      setLoader(false);
    }
  };

  const renderCTAOptions = () =>
    staticOptions?.PUBLIC_CTA_OPTIONS?.map(({ value, label, description }) => (
      <Box key={value}>
        <CustomCheckbox
          checked={selectedCTA === value}
          onChange={() => setSelectedCTA(value)}
          label={
            <Typography className="title-text fw600">
              {label}
              <span className="fw400 ml4">{`${
                description ? ' - ' : ''
              }${description}`}</span>
            </Typography>
          }
        />
        {value === 'contact_info' && selectedCTA === 'contact_info' && (
          <ContactForm
            formData={contactFormData}
            onFormDataChange={setContactFormData}
            validationErrors={validationErrors.contactForm}
          />
        )}
        {value === 'custom_link' && selectedCTA === 'custom_link' && (
          <ContactCtaLinkForm
            formData={ctaLinkFormData}
            onFormDataChange={setCtaLinkFormData}
            validationErrors={validationErrors.ctaLinkForm}
          />
        )}
      </Box>
    ));

  const renderPublicDisplayCheckboxes = () =>
    staticOptions?.PUBLIC_DISPLAY_FIELDS?.map((field) => (
      <Box key={field?.slug}>
        <CustomCheckbox
          checked={selectedFields?.[field?.slug]}
          onChange={() => handleFieldToggle(field?.slug)}
          label={<Typography className="title-text">{field?.name}</Typography>}
        />
      </Box>
    ));

  return (
    <Box className="recipe-settings-container h100">
      <Box className="section-right-title">
        <Typography className="sub-header-text">Recipe Settings</Typography>
      </Box>
      <Divider />

      <Box className="section-right-content">
        {/* Private Section */}
        <Box>
          <Typography className="sub-header-text text-underline">
            Private Recipe Visibility Settings
          </Typography>
          <Box className="pb8 pt8">
            <Typography className="title-text fw600">
              Highlight Changes for Assignee
            </Typography>
            <CustomSwitch
              label="Enable this to show highlighted changes to the assigned team member when viewing the recipe"
              checked={highlightDifferences}
              onChange={(e) => setHighlightDifferences(e?.target?.checked)}
            />
          </Box>
        </Box>

        {/* Public Settings */}
        <Box className="mt16">
          <Typography className="sub-header-text text-underline">
            Public Recipe Settings
          </Typography>
          <Box className="pb8 pt8">
            <Typography className="title-text fw600">
              Public Recipe Store Access
            </Typography>
            <CustomSwitch
              label="Enable to make public recipe features available. Turning this off hides all public recipe options."
              checked={publicRecipeEnabled}
              onChange={(e) => setPublicRecipeEnabled(e?.target?.checked)}
            />
          </Box>
        </Box>

        {/* CTA Selection */}
        <Box className="mt16">
          <Typography className="sub-header-text text-underline">
            Public Recipe Call-To-Action (CTA)
          </Typography>
          <Box className="pb8 pt8">
            <Typography className="title-text fw600">
              Choose what appears at the bottom of each public recipe page
            </Typography>
            {renderCTAOptions()}
          </Box>
        </Box>

        {/* Display Options */}
        <Box className="mt16">
          <Typography className="sub-header-text text-underline">
            Recipe Details to Display Publicly
          </Typography>
          <Box className="pb8 pt8">
            <Typography className="title-text fw600">
              Select which sections of the recipe to show publicly
            </Typography>
            <Box className="recipe-public-settings">
              {renderPublicDisplayCheckboxes()}
            </Box>
          </Box>
        </Box>
        <Box className="form-actions-btn">
          <CustomButton
            fullWidth
            variant="contained"
            type="submit"
            onClick={handleSubmit}
            disabled={loader}
            title={`${loader ? 'Saving...' : 'Save'}`}
          />
        </Box>
      </Box>
    </Box>
  );
}
