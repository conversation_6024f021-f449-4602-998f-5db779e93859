@import '@/styles/variable.scss';

.opened-layout {
  .emp-contract-section {
    padding-left: 20px !important;
    padding-top: 69px !important;
  }
}
.document-section-wrapper {
  display: flex;
  gap: var(--spacing-md);
  align-items: flex-start;
  height: 100%;
  @media (max-width: 768px) {
    flex-direction: column;
  }
  .folder-section-left {
    max-width: 250px;
    width: 100%;
    background-color: var(--color-white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow-xs);
    height: 100%;
    overflow-x: scroll;
    height: calc(100vh - 100px - var(--banner-height));
    @media (max-width: 899px) {
      margin-left: 0px;
    }
    @media (max-width: 768px) {
      margin-top: 77px;
      overflow-x: auto;
      width: 100%;
      height: 100%;
    }
    .folder-section-left-title {
      padding: var(--spacing-md);
    }
    .folder-side-menu-list {
      padding: var(--spacing-none) var(--spacing-lg);
      height: calc(100% - 49px);
      overflow: auto;
    }
  }
  .folder-section-right {
    width: 100%;
    background-color: var(--color-white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow-xs);
    height: 100%;
    overflow: hidden;
    .folder-section-right-content {
      padding: var(--spacing-xxl);
      height: calc(
        100% - 24px
      ); // change claculate base on tab height and scroll height
      overflow: auto;
      .document-center {
        .emp-contract-folder-sec {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
          gap: 22px;
          @media (max-width: 1200px) {
            grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
          }
        }
        .emp-contract-folder-sec > div,
        .emp-contract-folder-sec > div > div {
          width: 100%;
          max-width: 250px !important;
        }
        .action-btn-icon {
          svg,
          path {
            // stroke: var(--icon-color-white);
            stroke: var(--icon-color-black);
          }
          &.active {
            svg,
            path {
              stroke: var(--icon-color-white);
            }
          }
        }
      }
      .grid-view-folder {
        width: 50px;
        height: 50px;
      }
    }
  }
}
.emp-contract-section {
  padding-left: 20px;
  padding-top: 69px;
  width: 100%;
  @media (max-width: 768px) {
    padding-top: 0px !important;
  }
  .document-center {
    background-color: var(--color-white);
    padding: 26px 15px;
    margin-top: 24px;
    border-radius: 12px;
    width: 100%;
    .search-contianer {
      position: relative;
      .search-clear-icon {
        position: absolute;
        top: 10px;
        right: 10px;
        svg {
          height: 20px;
          width: 20px;
          color: #0000008a;
          cursor: pointer;
        }
      }
    }
    .contract-file-search {
      max-width: 100% !important;
      .MuiInputBase-input {
        padding-right: 15px !important;
      }
    }

    .selected-file {
      padding: 2px 8px;
      &:hover {
        background: var(--color-light-grayish-blue);
        border-radius: 10px;
      }
    }
  }
  .document-user-own-page {
    ul,
    ol {
      padding-left: 20px;
    }
  }
}
.disable-drag {
  pointer-events: none !important;
}
.document-center-section {
  .document-center {
    padding: var(--spacing-xxl);
    height: calc(100% - 24px);
    overflow: auto;
  }
  .grid-list-view-btn {
    .MuiButton-outlined svg path {
      stroke: var(--btn-color-primary);
    }

    .MuiButton-contained svg path {
      stroke: var(--btn-text-color-white);
    }
  }
  .document-center-tab {
    margin-top: 8px;
    .tab-list-sec {
      border-bottom: 1px solid #ededed;
      .tab-name {
        text-transform: none;
        font-family: $PrimaryFont;
        font-size: 16px;
        line-height: 20px;
        font-weight: 400;
        color: $color-Black;
        opacity: 1;
        width: 50%;
        text-transform: capitalize;
      }
      .MuiTabs-indicator {
        background-color: $color-primary;
        height: 3px;
      }
      .Mui-selected {
        color: $color-primary;
        // background-color: $color-primary;
        // border-radius: 20px;
      }
    }
  }
  .document-tab-panel {
    padding: 24px 0;
  }
  .document-filter-view {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .document-filter {
      display: flex;
      gap: 12px;
      align-items: center;
      width: calc(100% - 77px - 12px);
    }
    @media (max-width: 599px) {
      flex-direction: column;
      row-gap: 15px;
      align-items: flex-end;

      .document-filter {
        width: calc(100%);
        flex-direction: column;
        row-gap: 15px;
        align-items: center;
      }
    }
  }
  .document-all-action {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    margin-bottom: 24px;
    margin-top: 8px;
    .document-action-tooltip {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      gap: 16px;
      padding: 8px 12px;
      border-radius: 20px;
      width: 100%;
      background-color: var(--color-light-blue);

      .selected-category {
        border-right: 1px solid var(--color-dark-blue);
        padding-right: 15px;
        color: var(--color-dark-blue);
        svg {
          width: 24px;
          height: 24px;
          fill: var(--color-dark-blue) !important;
        }
      }
      .action-section.action-reset {
        svg {
          fill: none !important;
          stroke: var(--color-dark-blue);
        }
      }
      .action-section:last-child {
        border-right: 0;
      }
      .action-section {
        display: flex;
        align-items: center;
        svg {
          margin-right: 5px;
          cursor: pointer;
          fill: var(--color-dark-blue) !important;
        }
        .svg-icon {
          width: 21px;
          height: 21px;
        }
      }
    }
  }
  .category-details-action {
    display: flex;
    gap: 10px;
    // .Category-actions {
    //   margin: 0;
    //   .document-action-tooltip {
    //     padding: 0;
    //     padding: 4px 7px 3px;
    //     border-radius: 4px;
    //     .action-section {
    //       svg {
    //         width: 21px;
    //         height: 21px;
    //       }
    //     }
    //   }
    // }
    .Category-actions {
      background: $action-bg-color;
      padding: 5px;
      border-radius: 4px;
      height: 28px;
      .border-svg {
        fill: $color-Black !important;
      }
      svg {
        fill: $color-Black !important;
        width: 18px !important;
        height: 18px !important;
      }
      &:hover {
        background: $color-Dark-10;
      }
    }
    .action-icon {
      height: max-content;
    }
  }
  .grid-view-file {
    width: 100px;
    height: 100px;
    display: flex;
    justify-content: center;
    align-items: center;
    .grid-view-file-image {
      width: 80px;
      height: 80px;
      object-fit: cover;
      border-radius: 8px;
    }
  }
  .grid-view-folder {
    width: 100px;
    height: 100px;
  }
  .list-view-file {
    width: 40px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    .list-view-file-image {
      width: 30px;
      height: 30px;
      object-fit: cover;
      border-radius: 5px;
    }
  }
  .emp-contract-folder-sec {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 22px;
    @media (max-width: 1200px) {
      grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    }
  }

  .emp-contract-folder-sec > div,
  .emp-contract-folder-sec > div > div {
    width: 100%;
    max-width: 250px !important;
    // display: block !important;
  }
  .custom-breadcrumbs {
    display: flex;
    flex-wrap: wrap;
    position: relative;
    align-items: center;
    svg {
      width: 18px;
      height: 18px;
    }
    .grid-icon {
      width: max-content;
      height: max-content;
      svg {
        width: auto !important;
        height: auto !important;
      }
    }
    .home-grid-icon {
      width: 24px;
      height: 24px;
      margin-right: 8px;
      svg {
        margin-left: 0 !important;
      }
    }
    .breadcrumbs-menu {
      display: flex;
      justify-content: flex-start;
      align-items: center;

      svg {
        margin-left: 6px;
      }

      .separator-icon {
        margin: 0 10px;
      }
    }

    .active-breadcrumb {
      color: $color-Black;

      &:hover {
        text-decoration: underline;
        text-underline-offset: 3px;
        cursor: pointer;
      }
    }

    .last-breadcrumb {
      // color: $color-green;
      color: $color-Black;
      text-decoration: underline;
      text-underline-offset: 3px;
    }
  }
}
.move-to-list {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px;
  .move-btn {
    display: none;
  }
  &:hover {
    background: $color-Primary-20;
    border-radius: 8px;
    .move-btn {
      display: block;
      border: 1px solid var(--color-primary);
      border-radius: 20px;
      padding: 0px 5px;
      font-size: 14px !important;
      cursor: pointer;
    }
  }
}
