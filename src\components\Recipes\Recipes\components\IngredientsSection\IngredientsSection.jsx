import React, { useState, useEffect } from 'react';
import * as Yup from 'yup';
import Icon from '@/components/UI/AppIcon/AppIcon';
import CustomTextField from '@/components/UI/CustomTextField';
import CustomButton from '@/components/UI/CustomButton';
import CustomSelect from '@/components/UI/CustomSelect';
import { InputAdornment } from '@mui/material';
import { useRecipeMeasures } from '@/hooks/useRecipeMeasures';
import { useRouter } from 'next/navigation';
import './IngredientsSection.scss';

// Validation Schema
const ingredientValidationSchema = Yup.object().shape({
  name: Yup.string()
    .trim()
    .required('Ingredient name is required')
    .min(2, 'Name must be at least 2 characters'),
  quantity: Yup.number()
    .min(0, 'Quantity cannot be negative')
    .required('Quantity is required'),
  unit: Yup.string().required('Unit is required'),
  baseCost: Yup.number().min(0, 'Cost cannot be negative'),
  wastagePercentage: Yup.number()
    .min(0, 'Wastage cannot be negative')
    .max(100, 'Wastage cannot exceed 100%'),
});

const IngredientsSection = ({
  data,
  dispatch,
  validationErrors = {},
  currency,
}) => {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState('');
  const [showSuggestions, setShowSuggestions] = useState(false);
  const { unitsOfMeasureOptions } = useRecipeMeasures();
  const [localIngredients, setLocalIngredients] = useState(() => {
    // Handle both data.ingredients and data directly being an array
    return Array.isArray(data) ? data : data?.ingredients || [];
  });

  // Sync local state with prop changes
  useEffect(() => {
    // Handle both data.ingredients and data directly being an array
    const ingredientsData = Array.isArray(data)
      ? data
      : data?.ingredients || [];
    setLocalIngredients(ingredientsData);
  }, [data, data?.ingredients]);

  // Use local ingredients for immediate UI updates
  const ingredients = localIngredients;

  // Mock ingredient database
  const ingredientDatabase = [
    {
      id: 1,
      name: 'Chicken Breast',
      category: 'Protein',
      avgCost: 8.99,
      unit: 'lb',
      allergens: [],
      preparationMethod: 'whole',
      cookingMethod: 'raw',
      wastagePercentage: 0,
      quantity: 1,
    },
    {
      id: 2,
      name: 'Salmon Fillet',
      category: 'Protein',
      avgCost: 12.99,
      unit: 'lb',
      allergens: ['fish'],
      preparationMethod: 'whole',
      cookingMethod: 'raw',
      wastagePercentage: 2,
      quantity: 2,
    },
    {
      id: 3,
      name: 'Ground Beef',
      category: 'Protein',
      avgCost: 6.99,
      unit: 'lb',
      allergens: [],
      preparationMethod: 'whole',
      cookingMethod: 'raw',
      wastagePercentage: 0,
      quantity: 2,
    },
    {
      id: 4,
      name: 'Olive Oil',
      category: 'Oil',
      avgCost: 4.99,
      unit: 'bottle',
      allergens: [],
      preparationMethod: 'whole',
      cookingMethod: 'raw',
      wastagePercentage: 0,
      quantity: 5,
    },
    {
      id: 5,
      name: 'Garlic',
      category: 'Vegetable',
      avgCost: 0.99,
      unit: 'head',
      allergens: [],
      preparationMethod: 'whole',
      cookingMethod: 'raw',
      wastagePercentage: 0,
      quantity: 1,
    },
    {
      id: 6,
      name: 'Onion',
      category: 'Vegetable',
      avgCost: 1.49,
      unit: 'lb',
      allergens: [],
      preparationMethod: 'whole',
      cookingMethod: 'raw',
      wastagePercentage: 0,
      quantity: 1,
    },
    {
      id: 7,
      name: 'Tomatoes',
      category: 'Vegetable',
      avgCost: 2.99,
      unit: 'lb',
      allergens: [],
      preparationMethod: 'whole',
      cookingMethod: 'raw',
      wastagePercentage: 0,
      quantity: 1,
    },
    {
      id: 8,
      name: 'Bell Pepper',
      category: 'Vegetable',
      avgCost: 1.99,
      unit: 'each',
      allergens: [],
      preparationMethod: 'whole',
      cookingMethod: 'raw',
      wastagePercentage: 0,
      quantity: 1,
    },
    {
      id: 9,
      name: 'Mushrooms',
      category: 'Vegetable',
      avgCost: 2.49,
      unit: 'lb',
      allergens: [],
      preparationMethod: 'whole',
      cookingMethod: 'raw',
      wastagePercentage: 0,
      quantity: 1,
    },
    {
      id: 10,
      name: 'Cheddar Cheese',
      category: 'Dairy',
      avgCost: 4.99,
      unit: 'lb',
      allergens: ['dairy'],
      preparationMethod: 'whole',
      cookingMethod: 'raw',
      wastagePercentage: 0,
      quantity: 1,
    },
    {
      id: 11,
      name: 'Heavy Cream',
      category: 'Dairy',
      avgCost: 3.49,
      unit: 'pint',
      allergens: ['dairy'],
    },
    {
      id: 12,
      name: 'Butter',
      category: 'Dairy',
      avgCost: 4.99,
      unit: 'lb',
      allergens: ['dairy'],
    },
    {
      id: 13,
      name: 'All-Purpose Flour',
      category: 'Grain',
      avgCost: 2.99,
      unit: 'lb',
      allergens: ['gluten'],
    },
    {
      id: 14,
      name: 'Rice',
      category: 'Grain',
      avgCost: 1.99,
      unit: 'lb',
      allergens: [],
    },
    {
      id: 15,
      name: 'Pasta',
      category: 'Grain',
      avgCost: 1.49,
      unit: 'lb',
      allergens: ['gluten'],
    },
  ];

  const cookingMethods = [
    { label: 'Raw', value: 'raw' },
    { label: 'Sautéed', value: 'sauteed' },
    { label: 'Grilled', value: 'grilled' },
    { label: 'Roasted', value: 'roasted' },
    { label: 'Boiled', value: 'boiled' },
    { label: 'Steamed', value: 'steamed' },
    { label: 'Fried', value: 'fried' },
    { label: 'Baked', value: 'baked' },
    { label: 'Braised', value: 'braised' },
    { label: 'Poached', value: 'poached' },
  ];

  const preparationMethods = [
    { label: 'Whole', value: 'whole' },
    { label: 'Diced', value: 'diced' },
    { label: 'Chopped', value: 'chopped' },
    { label: 'Minced', value: 'minced' },
    { label: 'Sliced', value: 'sliced' },
    { label: 'Julienned', value: 'julienned' },
    { label: 'Grated', value: 'grated' },
    { label: 'Pureed', value: 'pureed' },
    { label: 'Crushed', value: 'crushed' },
    { label: 'Marinated', value: 'marinated' },
  ];

  const filteredIngredients =
    ingredientDatabase?.filter?.((ingredient) =>
      ingredient?.name
        ?.toLowerCase?.()
        ?.includes?.(searchTerm?.toLowerCase?.() || '')
    ) || [];

  const validateIngredient = async (ingredient) => {
    try {
      await ingredientValidationSchema.validate(ingredient, {
        abortEarly: false,
      });
      return {};
    } catch (error) {
      const errors = {};
      error?.inner?.forEach?.((err) => {
        if (err?.path) {
          errors[err.path] = err?.message || 'Invalid value';
        }
      });
      return errors;
    }
  };

  const addIngredient = (ingredientData = null) => {
    const wastageMultiplier =
      1 + (ingredientData?.wastagePercentage || 0) / 100;
    const finalCost =
      (ingredientData?.quantity || 0) *
      (ingredientData?.avgCost || 0) *
      wastageMultiplier;

    const newIngredient = {
      id: Date.now(),
      name: ingredientData?.name || '',
      quantity: ingredientData?.quantity || 0,
      unit: ingredientData?.unit || 'g',
      wastagePercentage: 0,
      baseCost: ingredientData?.avgCost || 0,
      cookingMethod: 'raw',
      preparationMethod: 'whole',
      finalCost: finalCost || 0,
      allergens: ingredientData?.allergens || [],
      category: ingredientData?.category || '',
    };

    const updatedIngredients = [...(ingredients || []), newIngredient];

    // Update local state immediately for UI responsiveness
    setLocalIngredients(updatedIngredients);

    // Ensure dispatch is called properly
    if (dispatch) {
      dispatch({ type: 'UPDATE_INGREDIENTS', payload: updatedIngredients });
    }

    setSearchTerm('');
    setShowSuggestions(false);
  };

  const updateIngredient = async (index, field, value) => {
    if (!ingredients?.[index]) return;

    const updatedIngredients = [...(ingredients || [])];
    updatedIngredients[index] = {
      ...updatedIngredients[index],
      [field]: value,
    };

    // Recalculate final cost when relevant fields change
    if (['quantity', 'baseCost', 'wastagePercentage']?.includes?.(field)) {
      const ingredient = updatedIngredients[index];
      const wastageMultiplier = 1 + (ingredient?.wastagePercentage || 0) / 100;
      updatedIngredients[index].finalCost =
        (ingredient?.quantity || 0) *
        (ingredient?.baseCost || 0) *
        wastageMultiplier;
    }

    // Update local state immediately for UI responsiveness
    setLocalIngredients(updatedIngredients);

    // Always update the ingredient, validation is for display purposes only
    if (dispatch) {
      dispatch({ type: 'UPDATE_INGREDIENTS', payload: updatedIngredients });
    }

    // Validate for UI feedback (optional)
    validateIngredient(updatedIngredients[index]);
  };

  const removeIngredient = (index) => {
    const updatedIngredients =
      ingredients?.filter?.((_, i) => i !== index) || [];

    // Update local state immediately for UI responsiveness
    setLocalIngredients(updatedIngredients);

    if (dispatch) {
      dispatch({ type: 'UPDATE_INGREDIENTS', payload: updatedIngredients });
    }
  };

  const selectIngredientFromDatabase = (ingredientData) => {
    if (ingredientData) {
      addIngredient(ingredientData);
    }
  };

  const addFirstIngredient = () => {
    router.push('/recipes/ingredients/create');
  };

  return (
    <div className="ingredients-section">
      {/* Add Ingredient Search */}
      <div className="ingredients-section__search">
        <div className="ingredients-section__header">
          <h3 className="ingredients-section__title">Recipe Ingredients</h3>
          {/* <CustomButton
            onClick={() => addIngredient()}
            variant="contained"
            startIcon={<Icon name="Plus" size={16} />}
          >
            Add Custom
          </CustomButton> */}
        </div>

        <div className="ingredients-section__search-input">
          <CustomTextField
            value={searchTerm}
            onChange={(e) => {
              const value = e?.target?.value || '';
              setSearchTerm(value);
              setShowSuggestions(value.length > 0);
            }}
            onFocus={() => setShowSuggestions((searchTerm?.length || 0) > 0)}
            onBlur={() => {
              // Delay hiding suggestions to allow for clicks
              setTimeout(() => setShowSuggestions(false), 200);
            }}
            placeholder="Search ingredients database..."
            fullWidth
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Icon
                    name="Search"
                    size={20}
                    className="ingredients-section__search-icon"
                  />
                </InputAdornment>
              ),
            }}
          />
        </div>

        {/* Search Suggestions */}
        {showSuggestions && (filteredIngredients?.length || 0) > 0 && (
          <div className="ingredients-section__suggestions">
            {filteredIngredients?.slice?.(0, 10)?.map?.((ingredient) => (
              <button
                key={ingredient?.id}
                onClick={() => selectIngredientFromDatabase(ingredient)}
                className="ingredients-section__suggestion-item"
              >
                <div className="ingredients-section__suggestion-content">
                  <div className="ingredients-section__suggestion-icon">
                    <Icon name="Package" size={14} />
                  </div>
                  <div className="ingredients-section__suggestion-details">
                    <div className="ingredients-section__suggestion-name">
                      {ingredient?.name || 'Unknown Ingredient'}
                    </div>
                    <div className="ingredients-section__suggestion-meta">
                      {ingredient?.category || 'Unknown'} • {currency}
                      {ingredient?.avgCost?.toFixed?.(2) || '0.00'}/
                      {ingredient?.unit || 'unit'}
                    </div>
                  </div>
                </div>
                <Icon name="Plus" size={16} />
              </button>
            )) || []}
          </div>
        )}
      </div>

      {/* Ingredients List */}
      <div className="ingredients-section__list">
        {(ingredients?.length || 0) === 0 ? (
          <div className="ingredients-section__empty-state">
            <Icon
              name="Package"
              size={48}
              className="ingredients-section__empty-icon"
            />
            <h4 className="ingredients-section__empty-title">
              No ingredients added yet
            </h4>
            <p className="ingredients-section__empty-description">
              Search for ingredients above or add custom ingredients to get
              started
            </p>
            <CustomButton
              onClick={() => addFirstIngredient()}
              variant="contained"
            >
              Add First Ingredient
            </CustomButton>
          </div>
        ) : (
          ingredients?.map?.((ingredient, index) => (
            <div
              key={ingredient?.id || index}
              className="ingredients-section__item"
            >
              {/* Ingredient Header */}
              <div className="ingredients-section__item-header">
                <div className="ingredients-section__item-info">
                  <div className="ingredients-section__item-icon">
                    <Icon name="Package" size={16} color="#D97706" />
                  </div>
                  <div className="ingredients-section__item-details">
                    <input
                      type="text"
                      value={ingredient?.name || ''}
                      onChange={(e) =>
                        updateIngredient(index, 'name', e?.target?.value || '')
                      }
                      placeholder="Ingredient name"
                      className="ingredients-section__item-name"
                    />
                    {ingredient?.category && (
                      <div className="ingredients-section__item-category">
                        {ingredient.category}
                      </div>
                    )}
                  </div>
                </div>

                <button
                  onClick={() => removeIngredient(index)}
                  className="ingredients-section__remove-button"
                  aria-label={`Remove ${ingredient?.name || 'ingredient'}`}
                >
                  <Icon name="Trash2" size={16} color="currentColor" />
                </button>
              </div>

              {/* Quantity and Unit */}
              <div className="ingredients-section__grid-2">
                <div className="ingredients-section__field-group">
                  <CustomTextField
                    label="Quantity"
                    type="number"
                    value={ingredient?.quantity?.toString?.() || ''}
                    onChange={(e) =>
                      updateIngredient(
                        index,
                        'quantity',
                        parseFloat(e?.target?.value) || 0
                      )
                    }
                    inputProps={{ step: 0.01, min: 0 }}
                    fullWidth
                  />
                </div>

                <div className="ingredients-section__field-group">
                  <CustomSelect
                    label="Unit"
                    name={`unit`}
                    fullWidth
                    options={unitsOfMeasureOptions}
                    value={
                      unitsOfMeasureOptions?.find((item) => {
                        return item?.value === ingredient?.unit;
                      }) || ''
                    }
                    onChange={(e) =>
                      updateIngredient(index, 'unit', e?.value || 'g')
                    }
                  />
                </div>
              </div>

              {/* Cost and Wastage */}
              <div className="ingredients-section__grid-3">
                <div className="ingredients-section__field-group">
                  <CustomTextField
                    label="Base Cost"
                    type="number"
                    value={ingredient?.baseCost?.toString?.() || ''}
                    onChange={(e) =>
                      updateIngredient(
                        index,
                        'baseCost',
                        parseFloat(e?.target?.value) || 0
                      )
                    }
                    inputProps={{ step: 0.01, min: 0 }}
                    fullWidth
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          {currency}
                        </InputAdornment>
                      ),
                    }}
                  />
                </div>

                <div className="ingredients-section__field-group">
                  <CustomTextField
                    label="Wastage"
                    type="number"
                    value={ingredient?.wastagePercentage || 0}
                    onChange={(e) =>
                      updateIngredient(
                        index,
                        'wastagePercentage',
                        parseInt(e?.target?.value) || 0
                      )
                    }
                    fullWidth
                    InputProps={{
                      endAdornment: (
                        <InputAdornment position="end">%</InputAdornment>
                      ),
                    }}
                  />
                </div>

                <div className="ingredients-section__field-group">
                  <CustomTextField
                    label="Final Cost"
                    value={(ingredient?.finalCost || 0)?.toFixed?.(2) || '0.00'}
                    fullWidth
                    disabled
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          {currency}
                        </InputAdornment>
                      ),
                    }}
                  />
                </div>
              </div>

              {/* Preparation Methods */}
              <div className="ingredients-section__grid-2">
                <div className="ingredients-section__field-group">
                  <CustomSelect
                    label="Cooking Method"
                    name={`cookingMethod-${index}`}
                    fullWidth
                    options={cookingMethods}
                    value={
                      cookingMethods?.find((item) => {
                        return item?.value === ingredient?.cookingMethod;
                      }) || ''
                    }
                    onChange={(e) =>
                      updateIngredient(
                        index,
                        'cookingMethod',
                        e?.value || 'raw'
                      )
                    }
                  />
                </div>

                <div className="ingredients-section__field-group">
                  <CustomSelect
                    label="Preparation"
                    name={`preparationMethod-${index}`}
                    fullWidth
                    options={preparationMethods}
                    value={
                      preparationMethods?.find((item) => {
                        return item?.value === ingredient?.preparationMethod;
                      }) || ''
                    }
                    onChange={(e) =>
                      updateIngredient(
                        index,
                        'preparationMethod',
                        e?.value || 'whole'
                      )
                    }
                  />
                </div>
              </div>

              {/* Allergens Display */}
              {ingredient?.allergens &&
                (ingredient?.allergens?.length || 0) > 0 && (
                  <div className="ingredients-section__allergens">
                    <Icon name="AlertTriangle" size={14} color="#F59E0B" />
                    <span className="ingredients-section__allergens-text">
                      Contains: {ingredient?.allergens?.join?.(', ') || ''}
                    </span>
                  </div>
                )}
            </div>
          ))
        )}
      </div>

      {/* Ingredients Summary */}
      {(ingredients?.length || 0) > 0 && (
        <div className="ingredients-section__summary">
          <h4 className="ingredients-section__summary-title">
            Ingredients Summary
          </h4>
          <div className="ingredients-section__summary-grid">
            <div className="ingredients-section__summary-item">
              <div className="ingredients-section__summary-value">
                {ingredients?.length || 0}
              </div>
              <div className="ingredients-section__summary-label">
                Total Ingredients
              </div>
            </div>
            <div className="ingredients-section__summary-item">
              <div className="ingredients-section__summary-value">
                {currency}
                {ingredients
                  ?.reduce?.((sum, ing) => sum + (ing?.finalCost || 0), 0)
                  ?.toFixed?.(2) || '0.00'}
              </div>
              <div className="ingredients-section__summary-label">
                Total Cost
              </div>
            </div>
            <div className="ingredients-section__summary-item">
              <div className="ingredients-section__summary-value">
                {[
                  ...new Set(
                    ingredients?.flatMap?.((ing) => ing?.allergens || []) || []
                  ),
                ]?.length || 0}
              </div>
              <div className="ingredients-section__summary-label">
                Unique Allergens
              </div>
            </div>
          </div>
        </div>
      )}

      {validationErrors?.ingredients && (
        <div className="ingredients-section__error">
          <Icon name="AlertCircle" size={16} />
          <span className="other-field-error-text">
            {validationErrors.ingredients}
          </span>
        </div>
      )}
    </div>
  );
};

export default IngredientsSection;
