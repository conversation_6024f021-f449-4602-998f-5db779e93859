body {
  .add-edit-recipe-container {
    padding: var(--spacing-none) !important;
    // Add Edit Recipe Component Styles
    .add-edit-recipe {
      // Main layout
      &__layout {
        display: flex;
      }

      // Desktop Sidebar
      &__sidebar {
        width: 320px; // w-80 = 20rem = 320px
        // background-color: var(--color-secondary);
        border-right: var(--border-width-xs) solid
          var(--border-color-light-gray);
        // height: 100vh;
        // position: sticky;
        // top: 64px; // top-16 = 4rem = 64px
        // overflow-y: auto;

        @media (max-width: 767px) {
          display: none;
        }
      }

      &__sidebar-content {
        padding: var(--spacing-lg) var(--spacing-xxl);
      }

      &__sidebar-title {
        font-family: var(--font-family-primary);
        font-weight: var(--font-weight-semibold);
        font-size: var(--font-size-lg);
        color: var(--text-color-black);
        margin-bottom: var(--spacing-xxl);
      }

      // Section Navigation
      &__section-nav {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-sm);
      }

      &__section-button {
        width: 100%;
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
        padding: var(--spacing-md) var(--spacing-lg);
        border-radius: var(--border-radius-md);
        text-align: left;
        transition: all 0.15s ease-out;
        border: none;
        background: none;
        cursor: pointer;
        color: var(--text-color-black);

        &:hover {
          background-color: var(--color-primary-opacity);
          color: var(--color-primary);
          svg {
            stroke: var(--color-primary);
          }
        }

        &--active {
          background-color: var(--color-primary);
          color: var(--text-color-white);
          box-shadow: var(--box-shadow-xs);
        }
      }

      &__section-content {
        flex: 1;
      }

      &__section-title {
        font-family: var(--font-family-primary);
        font-weight: var(--font-weight-medium);
        font-size: var(--font-size-sm);
      }

      &__section-required {
        font-size: var(--font-size-sm);
        color: var(--text-error);
        margin-left: var(--spacing-xxs);
      }

      // Cost Summary
      &__cost-summary {
        margin-top: var(--spacing-2xl);
        padding: var(--spacing-lg);
        background-color: var(--color-primary-opacity);
        border-radius: var(--border-radius-md);
        border: var(--border-width-xs) solid var(--color-primary);
      }

      &__cost-title {
        font-family: var(--font-family-primary);
        font-weight: var(--font-weight-semibold);
        font-size: var(--font-size-sm);
        color: var(--color-primary);
        margin-bottom: var(--spacing-md);
      }

      &__cost-items {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-sm);
        font-size: var(--font-size-sm);
      }

      &__cost-item {
        display: flex;
        justify-content: space-between;
      }

      &__cost-label {
        font-family: var(--font-family-primary);
        color: var(--text-color-slate-gray);
      }

      &__cost-value {
        font-family: var(--font-family-primary);
        font-weight: var(--font-weight-medium);
        color: var(--text-color-primary);
      }

      // Main Content Area
      &__main-content {
        flex: 1;
      }

      // Header Actions
      &__header {
        // background-color: var(--color-secondary);
        border-bottom: var(--border-width-xs) solid
          var(--border-color-light-gray);
        padding: var(--spacing-lg) var(--spacing-lg);

        @media (min-width: 1024px) {
          padding: var(--spacing-lg) var(--spacing-xxl);
        }
      }

      &__header-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
      }

      &__header-left {
        display: flex;
        align-items: center;
        gap: var(--spacing-lg);
      }

      &__header-title {
        font-family: var(--font-family-primary);
        font-weight: var(--font-weight-semibold);
        font-size: var(--font-size-lg);
        color: var(--text-color-black);
      }

      &__header-actions {
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
      }

      // Action Buttons
      &__preview-button {
        .custom-btn {
          background-color: var(--color-secondary) !important;
          color: var(--text-color-white) !important;
          border-color: var(--color-secondary) !important;

          &:hover {
            background-color: var(--color-dark-50) !important;
          }
        }
      }

      &__save-button {
        .custom-btn {
          background-color: var(--color-secondary) !important;
          color: var(--text-color-primary) !important;
          border-color: var(--border-color-light-gray) !important;

          &:hover {
            background-color: var(--color-off-white) !important;
          }
        }
      }

      // Mobile Section Selector
      &__mobile-selector {
        background-color: var(--color-secondary);
        border-bottom: var(--border-width-xs) solid
          var(--border-color-light-gray);
        padding: var(--spacing-md) var(--spacing-lg);

        @media (min-width: 768px) {
          display: none;
        }
      }

      &__mobile-nav {
        display: flex;
        align-items: center;
        justify-content: space-between;
      }

      &__mobile-nav-button {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        padding: var(--spacing-md) var(--spacing-md);
        color: var(--text-color-slate-gray);
        background: none;
        border: none;
        cursor: pointer;

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }
      }

      &__mobile-nav-text {
        font-family: var(--font-family-primary);
        font-size: var(--font-size-sm);
      }

      &__mobile-counter {
        text-align: center;
      }

      &__mobile-counter-text {
        font-family: var(--font-family-primary);
        font-size: var(--font-size-xxs);
        color: var(--text-color-slate-gray);
      }

      // Form Content
      &__form-content {
        padding: var(--spacing-lg) var(--spacing-xxl);

        // @media (min-width: 1024px) {
        //   padding: var(--spacing-xxl);
        // }
      }
    }

    // Responsive utilities
    .add-edit-recipe-hidden-mobile {
      @media (max-width: 767px) {
        display: none !important;
      }
    }

    .add-edit-recipe-hidden-desktop {
      @media (min-width: 768px) {
        display: none !important;
      }
    }

    // Icon spacing for buttons
    .add-edit-recipe__button-icon {
      margin-right: var(--spacing-sm);

      @media (max-width: 639px) {
        margin-right: 0;
      }
    }

    .add-edit-recipe__button-text {
      @media (max-width: 639px) {
        display: none;
      }
    }
  }
}
