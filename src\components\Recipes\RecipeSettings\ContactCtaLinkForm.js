'use client';

import React from 'react';
import { Box } from '@mui/material';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import CustomTextField from '@/components/UI/CustomTextField';
import './recipesettings.scss';

const ContactCtaLinkForm = ({
  formData = {},
  onFormDataChange,
  validationErrors = {},
}) => {
  return (
    <Box className="">
      <Formik
        initialValues={formData}
        enableReinitialize={true}
        validationSchema={Yup.object().shape({
          text: Yup.string().trim().required('This field is required'),
          link: Yup.string()
            .trim()
            .required('This field is required')
            .url('Invalid URL'),
        })}
        onSubmit={async (requestData) => {
          //   setLoader(true);
          let sendData = {
            text: requestData?.text?.trim(),
            link: requestData?.link?.trim(),
          };
          console.error('sendData', sendData);
        }}
      >
        {({
          errors,
          touched,
          handleBlur,
          values,
          handleSubmit,
          handleChange,
        }) => (
          <Form onSubmit={handleSubmit}>
            <Box className="recipe-settings-form d-flex align-start gap-sm pb4">
              <Box className="contact-form-fields">
                <CustomTextField
                  fullWidth
                  name="text"
                  value={values?.text}
                  label="Text"
                  placeholder="Enter action button text"
                  error={Boolean(
                    (touched?.text && errors?.text) || validationErrors?.text
                  )}
                  helperText={
                    (touched?.text && errors?.text) || validationErrors?.text
                  }
                  onBlur={handleBlur}
                  onChange={(e) => {
                    handleChange(e);
                    if (onFormDataChange) {
                      onFormDataChange({
                        ...values,
                        [e.target.name]: e.target.value,
                      });
                    }
                  }}
                  required
                />
              </Box>
              <Box className="contact-form-fields">
                <CustomTextField
                  fullWidth
                  name="link"
                  value={values?.link}
                  label="Link"
                  placeholder="Enter link"
                  error={Boolean(
                    (touched?.link && errors?.link) || validationErrors?.link
                  )}
                  helperText={
                    (touched?.link && errors?.link) || validationErrors?.link
                  }
                  onBlur={handleBlur}
                  onChange={(e) => {
                    handleChange(e);
                    if (onFormDataChange) {
                      onFormDataChange({
                        ...values,
                        [e.target.name]: e.target.value,
                      });
                    }
                  }}
                  required
                />
              </Box>
            </Box>
          </Form>
        )}
      </Formik>
    </Box>
  );
};

export default ContactCtaLinkForm;
