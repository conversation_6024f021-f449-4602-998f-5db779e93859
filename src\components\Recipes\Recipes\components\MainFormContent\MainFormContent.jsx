'use client';

import React from 'react';
import Icon from '@/components/UI/AppIcon/AppIcon';
import CustomButton from '@/components/UI/CustomButton';
import AutoSaveIndicator from '../AutoSaveIndicator/AutoSaveIndicator';
import './MainFormContent.scss';

export default function MainFormContent({
  formSections,
  activeSection,
  onSectionChange,
  currentSection,
  CurrentSectionComponent,
  formState,
  dispatch,
  validationErrors,
  isMobileView,
  currency,
  isUpdate,
  onPreview,
  onSaveAndExit,
}) {
  return (
    <div className="main-form-content">
      {/* Header Actions */}
      <div className="main-form-content__header">
        <div className="main-form-content__header-content">
          <div className="main-form-content__header-left">
            <h1 className="main-form-content__header-title">
              {isMobileView
                ? currentSection?.title
                : isUpdate
                  ? 'Update Recipe'
                  : 'Create Recipe'}
            </h1>
            <AutoSaveIndicator
              status={'saved'}
              lastSaved={'2025-06-10 08:30:00'}
            />
          </div>

          <div className="main-form-content__header-actions">
            <CustomButton
              onClick={onPreview}
              className="main-form-content__preview-button"
              variant="outlined"
              startIcon={<Icon name="Eye" size={16} />}
            >
              <span className="main-form-content__button-text">Preview</span>
            </CustomButton>

            <CustomButton
              onClick={onSaveAndExit}
              className="main-form-content__save-button"
              variant="contained"
              startIcon={<Icon name="Save" size={16} />}
            >
              <span className="main-form-content__button-text">
                Save & Exit
              </span>
            </CustomButton>
          </div>
        </div>
      </div>

      {/* Mobile: Section Selector */}
      {isMobileView && (
        <div className="main-form-content__mobile-selector">
          <div className="main-form-content__mobile-nav">
            <button
              onClick={() => {
                const currentIndex = formSections.findIndex(
                  (s) => s.id === activeSection
                );
                if (currentIndex > 0) {
                  onSectionChange(formSections[currentIndex - 1].id);
                }
              }}
              disabled={
                formSections.findIndex((s) => s.id === activeSection) === 0
              }
              className="main-form-content__mobile-nav-button"
            >
              <Icon name="ChevronLeft" size={16} color="currentColor" />
              <span className="main-form-content__mobile-nav-text">
                Previous
              </span>
            </button>

            <div className="main-form-content__mobile-counter">
              <span className="main-form-content__mobile-counter-text">
                {formSections.findIndex((s) => s.id === activeSection) + 1} of{' '}
                {formSections.length}
              </span>
            </div>

            <button
              onClick={() => {
                const currentIndex = formSections.findIndex(
                  (s) => s.id === activeSection
                );
                if (currentIndex < formSections.length - 1) {
                  onSectionChange(formSections[currentIndex + 1].id);
                }
              }}
              disabled={
                formSections.findIndex((s) => s.id === activeSection) ===
                formSections.length - 1
              }
              className="main-form-content__mobile-nav-button"
            >
              <span className="main-form-content__mobile-nav-text">Next</span>
              <Icon name="ChevronRight" size={16} color="currentColor" />
            </button>
          </div>
        </div>
      )}

      {/* Form Section Content */}
      <div className="main-form-content__form-content">
        {CurrentSectionComponent && (
          <CurrentSectionComponent
            data={formState[activeSection] || formState}
            dispatch={dispatch}
            validationErrors={validationErrors}
            isMobileView={isMobileView}
            currency={currency}
          />
        )}
      </div>
    </div>
  );
}
