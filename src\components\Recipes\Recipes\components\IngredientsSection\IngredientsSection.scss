@import '@/styles/variable.scss';

// Ingredients Section Component Styles
.ingredients-section {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);

  // Header Section
  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-lg);
  }

  &__title {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-lg);
    color: var(--text-color-black);
  }

  // Search Section
  &__search {
    position: relative;
  }

  &__search-icon {
    color: var(--text-color-slate-gray);
  }

  // Search Suggestions
  &__suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    margin-top: var(--spacing-tiny);
    background-color: var(--color-white);
    border: var(--border-width-xs) solid var(--border-color-light-gray);
    border-radius: var(--border-radius-md);
    box-shadow: var(--box-shadow-lg);
    z-index: 200;
    max-height: 240px;
    overflow-y: auto;
  }

  &__suggestion-item {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md) var(--spacing-lg);
    text-align: left;
    background: none;
    border: none;
    cursor: pointer;
    transition: all 0.15s ease-out;

    &:hover {
      background-color: var(--color-off-white);
    }
  }

  &__suggestion-content {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
  }

  &__suggestion-icon {
    width: var(--spacing-2xl);
    height: var(--spacing-2xl);
    background-color: var(--color-primary-opacity);
    border-radius: var(--border-radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--icon-color-primary);
  }

  &__suggestion-details {
    display: flex;
    flex-direction: column;
  }

  &__suggestion-name {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-sm);
    color: var(--text-color-black);
  }

  &__suggestion-meta {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xxs);
    color: var(--text-color-slate-gray);
  }

  // Empty State
  &__empty-state {
    text-align: center;
    padding: var(--spacing-3xl) var(--spacing-lg);
    border: var(--border-width-sm) dashed var(--border-color-light-gray);
    border-radius: var(--border-radius-md);
  }

  &__empty-icon {
    margin: 0 auto var(--spacing-lg);
    color: var(--color-light-gray);
  }

  &__empty-title {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    color: var(--text-color-primary);
    margin-bottom: var(--spacing-sm);
  }

  &__empty-description {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    color: var(--text-color-slate-gray);
    margin-bottom: var(--spacing-lg);
  }

  // Ingredients List
  &__list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
  }

  // Individual Ingredient Item
  &__item {
    background-color: var(--color-white);
    border: var(--border-width-xs) solid var(--border-color-light-gray);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
  }

  &__item-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  &__item-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
  }

  &__item-icon {
    width: calc(var(--spacing-lg) + var(--spacing-sm));
    height: calc(var(--spacing-lg) + var(--spacing-sm));
    background-color: var(--color-primary-opacity);
    border-radius: var(--border-radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &__item-details {
    display: flex;
    flex-direction: column;
  }

  &__item-name {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-sm);
    color: var(--text-color-primary);
    background: transparent;
    border: none;
    outline: none;
    padding: var(--field-padding);
    border-radius: var(--field-radius);
    transition: all 0.15s ease-out;

    &:focus {
      background-color: var(--color-white);
      border: var(--field-border);
    }
  }

  &__item-category {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xxs);
    color: var(--text-color-slate-gray);
  }

  &__remove-button {
    padding: var(--spacing-sm);
    color: var(--color-danger);
    background: none;
    border: none;
    border-radius: var(--border-radius-md);
    cursor: pointer;
    transition: all 0.15s ease-out;

    &:hover {
      background-color: var(--color-danger-opacity);
    }
  }

  // Form Grids
  &__grid-2 {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);

    @media (min-width: 768px) {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  &__grid-3 {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);

    @media (min-width: 768px) {
      grid-template-columns: repeat(3, 1fr);
    }
  }

  // Field Groups
  &__field-group {
    display: flex;
    flex-direction: column;
  }

  &__field-label {
    display: block;
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-xxs);
    color: var(--text-color-slate-gray);
    margin-bottom: var(--spacing-tiny);
  }

  // Wastage Slider
  &__wastage-slider {
    width: 100%;
    height: var(--spacing-sm);
    border-radius: var(--border-radius-full);
    background: var(--color-light-gray);
    outline: none;
    appearance: none;

    &::-webkit-slider-thumb {
      appearance: none;
      width: var(--spacing-lg);
      height: var(--spacing-lg);
      border-radius: var(--border-radius-full);
      background: var(--color-primary);
      cursor: pointer;
    }

    &::-moz-range-thumb {
      width: var(--spacing-lg);
      height: var(--spacing-lg);
      border-radius: var(--border-radius-full);
      background: var(--color-primary);
      cursor: pointer;
      border: none;
    }
  }

  &__wastage-value {
    text-align: center;
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xxs);
    color: var(--text-color-slate-gray);
    margin-top: var(--spacing-tiny);
  }

  // Final Cost Display
  &__final-cost {
    padding: var(--spacing-md) var(--spacing-lg);
    background-color: var(--color-off-white);
    border: var(--border-width-xs) solid var(--border-color-light-gray);
    border-radius: var(--border-radius-md);
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    color: var(--text-color-primary);
  }

  // Allergens Display
  &__allergens {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    background-color: var(--color-muted-mustard-opacity);
    border: var(--border-width-xs) solid rgba(219, 151, 44, 0.2);
    border-radius: var(--border-radius-md);
  }

  &__allergens-text {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xs);
    color: var(--color-warning);
    font-weight: var(--font-weight-medium);
  }

  // Summary Section
  &__summary {
    background-color: var(--color-primary-opacity);
    border: var(--border-width-xs) solid rgba(19, 94, 150, 0.2);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
  }

  &__summary-title {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-sm);
    color: var(--color-primary);
    margin-bottom: var(--spacing-md);
  }

  &__summary-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);

    @media (min-width: 768px) {
      grid-template-columns: repeat(3, 1fr);
    }
  }

  &__summary-item {
    text-align: center;

    @media (min-width: 768px) {
      text-align: left;
    }
  }

  &__summary-value {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-base);
    color: var(--text-color-primary);
  }

  &__summary-label {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xs);
    color: var(--text-color-slate-gray);
    margin-top: var(--spacing-tiny);
  }

  // Error Display
  &__error {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--color-danger);
  }
}
