import React, { useState } from 'react';
import { Tooltip, Typography } from '@mui/material';
import Icon from '@/components/UI/AppIcon/AppIcon';
import './FormProgressIndicator.scss';

const FormProgressIndicator = () => {
  const [currentSection, setCurrentSection] = useState('basic-info');

  const formSections = [
    {
      id: 'basic-info',
      label: 'Basic Info',
      shortLabel: 'Basic',
      icon: 'FileText',
      completed: true,
      hasErrors: false,
    },
    {
      id: 'media',
      label: 'Media & Photos',
      shortLabel: 'Media',
      icon: 'Camera',
      completed: false,
      hasErrors: false,
    },
    {
      id: 'ingredients',
      label: 'Ingredients',
      shortLabel: 'Ingredients',
      icon: 'ShoppingCart',
      completed: true,
      hasErrors: false,
    },
    {
      id: 'instructions',
      label: 'Instructions',
      shortLabel: 'Steps',
      icon: 'List',
      completed: false,
      hasErrors: false,
    },
    {
      id: 'nutrition',
      label: 'Nutrition',
      shortLabel: 'Nutrition',
      icon: 'Heart',
      completed: false,
      hasErrors: true,
    },
    {
      id: 'serving',
      label: 'Serving',
      shortLabel: 'Serving',
      icon: 'Users',
      completed: false,
      hasErrors: false,
    },
    // {
    //   id: 'costing',
    //   label: 'Cost Analysis',
    //   shortLabel: 'Costs',
    //   icon: 'DollarSign',
    //   completed: false,
    //   hasErrors: false,
    // },
  ];

  const completedSections =
    formSections?.filter((section) => section?.completed)?.length || 0;
  const totalSections = formSections?.length || 0;
  const progressPercentage =
    totalSections > 0 ? (completedSections / totalSections) * 100 : 0;

  const handleSectionClick = (sectionId) => {
    setCurrentSection(sectionId);
  };

  const getSectionStatus = (section) => {
    if (section?.hasErrors) return 'error';
    if (section?.completed) return 'completed';
    if (section?.id === currentSection) return 'current';
    return 'pending';
  };

  const getSectionStyles = (status) => {
    switch (status) {
      case 'completed':
        return 'form-progress-indicator__section-button--completed';
      case 'current':
        return 'form-progress-indicator__section-button--current';
      case 'error':
        return 'form-progress-indicator__section-button--error';
      default:
        return 'form-progress-indicator__section-button--pending';
    }
  };

  const getMobileDotStyles = (status) => {
    switch (status) {
      case 'completed':
        return 'form-progress-indicator__mobile-dot--completed';
      case 'current':
        return 'form-progress-indicator__mobile-dot--current';
      case 'error':
        return 'form-progress-indicator__mobile-dot--error';
      default:
        return 'form-progress-indicator__mobile-dot--pending';
    }
  };

  return (
    <div className="form-progress-indicator">
      {/* Progress Bar */}
      <div className="form-progress-indicator__progress-section">
        <div className="form-progress-indicator__progress-header">
          <span className="form-progress-indicator__progress-title">
            Recipe Creation Progress
          </span>
          <span className="form-progress-indicator__progress-count">
            {completedSections}/{totalSections} sections completed
          </span>
        </div>
        <div className="form-progress-indicator__progress-bar">
          <div
            className="form-progress-indicator__progress-fill"
            style={{ width: `${progressPercentage}%` }}
          ></div>
        </div>
      </div>

      {/* Desktop: Horizontal Section Navigation */}
      <div className="form-progress-indicator__desktop-nav">
        {formSections?.map((section) => {
          const status = getSectionStatus(section);
          return (
            <button
              key={section?.id}
              onClick={() => handleSectionClick(section?.id)}
              className={`form-progress-indicator__section-button ${getSectionStyles(status)}`}
            >
              <Tooltip
                title={
                  <Typography className="sub-title-text">{`${section?.label} - ${status === 'completed' ? 'Completed' : status === 'error' ? 'Has errors' : 'In progress'}`}</Typography>
                }
                arrow
                classes={{
                  tooltip: 'info-tooltip-container',
                }}
              >
                <div className="form-progress-indicator__section-content">
                  {status === 'completed' ? (
                    <Icon name="CheckCircle" size={16} color="currentColor" />
                  ) : status === 'error' ? (
                    <Icon name="AlertCircle" size={16} color="currentColor" />
                  ) : (
                    <Icon name={section?.icon} size={16} color="currentColor" />
                  )}
                  <span className="form-progress-indicator__section-label">
                    {section?.label}
                  </span>
                </div>
              </Tooltip>
            </button>
          );
        })}
      </div>

      {/* Mobile: Simplified Progress Dots with Current Section */}
      <div className="form-progress-indicator__mobile-nav">
        <div className="form-progress-indicator__mobile-header">
          <div className="form-progress-indicator__mobile-current">
            <Icon
              name={formSections?.find((s) => s?.id === currentSection)?.icon}
              size={16}
              color="currentColor"
            />
            <span className="form-progress-indicator__mobile-title">
              {formSections?.find((s) => s?.id === currentSection)?.label}
            </span>
          </div>
          <span className="form-progress-indicator__mobile-counter">
            {(formSections?.findIndex((s) => s?.id === currentSection) ?? -1) +
              1}
            /{totalSections}
          </span>
        </div>

        <div className="form-progress-indicator__mobile-dots">
          {formSections?.map((section) => {
            const status = getSectionStatus(section);
            return (
              <button
                key={section?.id}
                onClick={() => handleSectionClick(section?.id)}
                className={`form-progress-indicator__mobile-dot ${getMobileDotStyles(status)}`}
                title={section?.label}
              />
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default FormProgressIndicator;
