@import '@/styles/variable.scss';

// Nutrition Section Component Styles
.nutrition-section {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);

  // Tab Navigation
  &__tabs {
    display: flex;
    gap: var(--spacing-sm);
    background-color: var(--color-primary-opacity);
    border-radius: var(--field-radius);
    padding: var(--spacing-xs);
  }

  &__tab {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--field-padding);
    border-radius: var(--field-radius);
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-sm);
    transition: all 0.15s ease-out;
    border: none;
    cursor: pointer;
    background: transparent;
    &--active {
      background-color: var(--color-white);
      color: var(--color-primary);
      box-shadow: var(--box-shadow-xs);
    }

    &--inactive {
      color: var(--text-color-slate-gray);

      &:hover {
        color: var(--text-color-primary);
      }
    }
  }

  // Header Section
  &__header {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  &__title {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-lg);
    color: var(--text-color-black);
  }

  &__description {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    color: var(--text-color-slate-gray);
  }

  // Nutrition Grid
  &__grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);

    @media (min-width: 768px) {
      grid-template-columns: repeat(2, 1fr);
    }

    @media (min-width: 1024px) {
      grid-template-columns: repeat(3, 1fr);
    }
  }

  &__grid-4 {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);

    @media (min-width: 768px) {
      grid-template-columns: repeat(2, 1fr);
    }

    @media (min-width: 1024px) {
      grid-template-columns: repeat(4, 1fr);
    }
  }

  // Field Groups
  &__field-group {
    display: flex;
    flex-direction: column;
  }

  &__field-label {
    display: block;
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-sm);
    color: var(--text-color-primary);
    margin-bottom: var(--spacing-sm);
  }

  // Search Section
  &__search {
    position: relative;
  }

  &__search-icon {
    color: var(--text-color-slate-gray);
  }

  // Icon Colors
  &__icon {
    &--orange {
      color: var(--icon-color-orange);
    }
    &--red {
      color: var(--icon-color-red);
    }
    &--yellow {
      color: var(--icon-color-warning);
    }
    &--blue {
      color: var(--icon-color-primary);
    }
    &--green {
      color: var(--icon-color-green);
    }
    &--pink {
      color: #db2777;
    }
    &--gray {
      color: var(--icon-color-slate-gray);
    }
    &--purple {
      color: #9333ea;
    }
  }

  // Field Label with Icon
  &__field-label {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-sm);
    color: var(--text-color-primary);
  }

  // Allergens Section
  &__allergens {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
  }

  &__allergens-title {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-sm);
    color: var(--text-color-primary);
  }

  &__allergens-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);

    @media (min-width: 768px) {
      grid-template-columns: repeat(3, 1fr);
    }

    @media (min-width: 1024px) {
      grid-template-columns: repeat(4, 1fr);
    }
  }

  &__allergen-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
    border: var(--field-border);
    border-radius: var(--border-radius-md);
    transition: all 0.15s ease-out;
    cursor: pointer;
    background: none;
    text-align: left;

    &--selected {
      background-color: var(--color-danger-opacity);
      border-color: var(--color-danger);
      color: var(--color-danger);
      &:hover {
        background-color: var(--color-danger-opacity);
      }
    }

    &--high {
      background-color: var(--color-danger-opacity);
      color: var(--color-danger);
    }

    &--medium {
      background-color: var(--color-warning-opacity);
      color: var(--color-warning);
    }

    &--low {
      background-color: var(--color-primary-opacity);
      color: var(--color-primary);
    }

    &--default {
      &:hover {
        background-color: var(--color-off-white);
      }
    }
  }

  &__allergen-checkbox {
    width: var(--spacing-lg);
    height: var(--spacing-lg);
    border: var(--border-width-sm) solid var(--border-color-light-gray);
    border-radius: var(--border-radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.15s ease-out;

    &--checked {
      background-color: var(--color-danger);
      border-color: var(--color-danger);
      color: var(--text-color-white);
    }
  }

  &__allergen-icon {
    width: var(--spacing-2xl);
    height: var(--spacing-2xl);
    border-radius: var(--border-radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.15s ease-out;

    &--default {
      background-color: var(--color-secondary);
      color: var(--text-color-slate-gray);
    }

    &--selected {
      background-color: var(--color-danger);
      color: var(--text-color-white);
    }
  }

  &__allergen-content {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  &__allergen-label {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    // color: var(--text-color-primary);
    // cursor: pointer;
  }

  &__allergen-severity {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xs);
    // color: var(--text-color-slate-gray);
    opacity: 0.75;
  }

  // Dietary Restrictions
  &__dietary {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
    padding: var(--spacing-lg);
    background-color: var(--color-danger-background);
    border: var(--border-width-xs) solid var(--border-color-red);
    border-radius: var(--border-radius-md);
  }

  &__dietary-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
  }

  &__dietary-title {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-sm);
    color: var(--color-danger);
  }

  &__dietary-grid {
    display: flex;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
    // display: grid;
    // grid-template-columns: repeat(2, 1fr);
    // gap: var(--spacing-md);

    // @media (min-width: 768px) {
    //   grid-template-columns: repeat(3, 1fr);
    // }

    // @media (min-width: 1024px) {
    //   grid-template-columns: repeat(4, 1fr);
    // }
  }

  &__dietary-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-xs) var(--spacing-md);
    border: var(--border-width-xs) solid var(--border-color-light-gray);
    border-radius: var(--border-radius-full);
    transition: all 0.15s ease-out;
    cursor: pointer;

    &--selected {
      background-color: var(--color-danger);
      color: var(--text-color-white);
    }
  }

  &__dietary-icon {
    width: var(--icon-size-xxs);
    height: var(--icon-size-xxs);
    color: var(--text-color-white);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &__dietary-content {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  &__dietary-label {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xxs);
    color: var(--text-color-white);
  }

  &__dietary-remove {
    padding: var(--spacing-tiny);
    color: var(--text-color-white);
    cursor: pointer;
    transition: all 0.15s ease-out;
    display: contents;
  }

  // Summary Section
  &__summary {
    padding: var(--spacing-lg);
    background-color: var(--color-primary-opacity);
    border: var(--border-width-xs) solid var(--color-primary);
    border-radius: var(--border-radius-md);
  }

  &__summary-title {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-semibold);
    font-size: var(--font-size-sm);
    color: var(--color-primary);
    margin-bottom: var(--spacing-md);
  }

  &__summary-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-lg);
    font-size: var(--font-size-sm);

    @media (min-width: 768px) {
      grid-template-columns: repeat(4, 1fr);
    }
  }

  &__summary-item {
    display: flex;
    flex-direction: column;
    text-align: center;
  }

  &__summary-value {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    color: var(--text-color-primary);
  }

  &__summary-label {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xxs);
    color: var(--text-color-slate-gray);
  }

  &__summary-unit {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xxs);
    color: var(--text-color-slate-gray);
  }

  // Content Area
  &__content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
  }

  // Vitamins Section
  &__vitamins {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
  }

  &__vitamins-title {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-sm);
    color: var(--text-color-primary);
  }
}
