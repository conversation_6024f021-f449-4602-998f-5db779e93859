@import '@/styles/variable.scss';

// Serving Details Section Component Styles
.serving-details-section {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xxl);

  // Section Headers
  &__section {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
  }

  &__header {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
  }

  &__title {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-lg);
    color: var(--text-color-black);
  }

  &__description {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    color: var(--text-color-slate-gray);
  }

  // Yield and Portions Section
  &__yield-section {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
  }

  &__yield-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);

    @media (min-width: 768px) {
      grid-template-columns: repeat(3, 1fr);
    }
  }

  &__yield-field {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  &__yield-input-group {
    display: flex;
    gap: var(--spacing-sm);
  }

  &__yield-value {
    flex: 1;
  }

  &__yield-unit {
    min-width: 120px;
  }

  &__portion-size {
    position: relative;
  }

  &__portion-size-unit {
    position: absolute;
    right: var(--spacing-md);
    top: 50%;
    transform: translateY(-50%);
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xxs);
    color: var(--text-color-slate-gray);
  }

  &__auto-note {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xxs);
    color: var(--text-color-slate-gray);
  }

  // Serving Instructions Section
  &__serving-section {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
  }

  &__serving-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);

    @media (min-width: 768px) {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  // Professional Tips Section
  &__tips-section {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
  }

  &__tips-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);

    @media (min-width: 1024px) {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  &__tip-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
  }

  &__tip-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
  }

  &__tip-label {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-sm);
    color: var(--text-color-primary);
  }

  &__tip-info {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
  }

  &__tip-info--foh {
    background-color: var(--color-success-opacity);
    border: var(--border-width-xs) solid var(--color-success);
  }

  &__tip-info--chef {
    background-color: var(--color-primary-opacity);
    border: var(--border-width-xs) solid var(--color-primary);
  }

  &__tip-info-icon {
    margin-top: var(--spacing-tiny);
  }

  &__tip-info-text {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xxs);
  }

  &__tip-info-text--foh {
    color: var(--color-success);
  }

  &__tip-info-text--chef {
    color: var(--color-primary);
  }

  &__tip-strong {
    font-weight: var(--font-weight-semibold);
  }

  // Summary Section
  &__summary {
    padding: var(--spacing-lg);
    background-color: var(--color-primary-opacity);
    border: var(--border-width-xs) solid var(--color-primary);
    border-radius: var(--border-radius-md);
  }

  &__summary-title {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-sm);
    color: var(--color-primary);
    margin-bottom: var(--spacing-md);
  }

  &__summary-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
    font-size: var(--font-size-sm);

    @media (min-width: 768px) {
      grid-template-columns: repeat(4, 1fr);
    }
  }

  &__summary-item {
    text-align: center;
  }

  &__summary-value {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    color: var(--text-color-primary);
  }

  &__summary-label {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xxs);
    color: var(--text-color-slate-gray);
  }

  // Custom Select Overrides
  .custom-select {
    .MuiSelect-select {
      &:focus {
        background-color: transparent;
      }
    }
  }
}
