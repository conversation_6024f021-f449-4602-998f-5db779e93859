'use client';

import React, { useContext, useEffect, useReducer, useState } from 'react';
import { Box } from '@mui/material';
import { useRouter } from 'next/navigation';
import TopBackPageButton from '@/components/TopBackPage/TopBackPage';
import FormProgressIndicator from './components/FormProgressIndicator/FormProgressIndicator';
import BasicInfoSection from './components/BasicInfoSection/BasicInfoSection';
import MediaUploadSection from './components/MediaUploadSection/MediaUploadSection';
import IngredientsSection from './components/IngredientsSection/IngredientsSection';
import InstructionsSection from './components/InstructionsSection/InstructionsSection';
import NutritionSection from './components/NutritionSection/NutritionSection';
import ServingDetailsSection from './components/ServingDetailsSection/ServingDetailsSection';
import Icon from '@/components/UI/AppIcon/AppIcon';
import CustomButton from '@/components/UI/CustomButton';
import AutoSaveIndicator from './components/AutoSaveIndicator/AutoSaveIndicator';
import DialogBox from '@/components/UI/Modalbox';
import ConfirmationModal from '@/components/UI/ConfirmationModal';
import AuthContext from '@/helper/authcontext';
import { getCurrencySymbol } from '@/helper/common/commonFunctions';
import './recipes.scss';

// Recipe form reducer
const recipeFormReducer = (state, action) => {
  switch (action.type) {
    case 'UPDATE_BASIC_INFO':
      return { ...state, basicInfo: { ...state.basicInfo, ...action.payload } };
    case 'UPDATE_MEDIA':
      return { ...state, media: { ...state.media, ...action.payload } };
    case 'UPDATE_INGREDIENTS':
      return { ...state, ingredients: action.payload };
    case 'UPDATE_INSTRUCTIONS':
      return { ...state, instructions: action.payload };
    case 'UPDATE_NUTRITION':
      return { ...state, nutrition: { ...state.nutrition, ...action.payload } };
    case 'UPDATE_SERVING':
      return { ...state, serving: { ...state.serving, ...action.payload } };
    case 'SET_ACTIVE_SECTION':
      return { ...state, activeSection: action.payload };
    case 'CALCULATE_TOTALS':
      const totalCost = state.ingredients.reduce(
        (sum, ing) => sum + (ing.finalCost || 0),
        0
      );
      const totalTime =
        (state.basicInfo.prepTime || 0) + (state.basicInfo.cookTime || 0);
      const portionCost =
        state.serving.totalPortions > 0
          ? totalCost / state.serving.totalPortions
          : 0;
      return {
        ...state,
        calculations: {
          totalCost,
          totalTime,
          portionCost,
        },
      };
    default:
      return state;
  }
};

const initialState = {
  activeSection: 'basic-info',
  basicInfo: {
    recipeName: '',
    publicDisplayName: '',
    categories: [],
    dietaryOptions: [],
    prepTime: 0,
    cookTime: 0,
    visibility: ['private'],
  },
  media: {
    mainImage: null,
    additionalImages: [],
    videos: [],
    documents: [],
  },
  ingredients: [],
  instructions: [],
  nutrition: {
    calories: 0,
    protein: 0,
    carbs: 0,
    fat: 0,
    fiber: 0,
    sugar: 0,
  },
  serving: {
    yield: { value: 0, unit: 'servings' },
    totalPortions: 0,
    singlePortionSize: 0,
    servingMethod: '',
    serveIn: '',
    garnish: '',
    fohTips: '',
    chefTips: '',
  },
  calculations: {
    totalCost: 0,
    totalTime: 0,
    portionCost: 0,
  },
};

export default function AddEditRecipe({ isUpdate }) {
  const router = useRouter();
  const { authState } = useContext(AuthContext);
  const [formState, dispatch] = useReducer(recipeFormReducer, initialState);
  const [isMobileView, setIsMobileView] = useState(false);
  const [showExitModal, setShowExitModal] = useState(false);
  const [validationErrors, setValidationErrors] = useState({});

  const currency = getCurrencySymbol(authState?.currency_details);

  const handleRedirect = () => {
    router.back();
  };

  // Check for mobile view
  useEffect(() => {
    const checkMobileView = () => {
      setIsMobileView(window.innerWidth < 768);
    };

    checkMobileView();
    window.addEventListener('resize', checkMobileView);
    return () => window.removeEventListener('resize', checkMobileView);
  }, []);

  // Auto-calculate totals when dependencies change
  useEffect(() => {
    dispatch({ type: 'CALCULATE_TOTALS' });
  }, [
    formState.ingredients,
    formState.basicInfo.prepTime,
    formState.basicInfo.cookTime,
    formState.serving.totalPortions,
  ]);

  const formSections = [
    {
      id: 'basic-info',
      title: 'Basic Information',
      icon: 'FileText',
      component: BasicInfoSection,
      required: true,
    },
    {
      id: 'media',
      title: 'Media & Photos',
      icon: 'Camera',
      component: MediaUploadSection,
      required: false,
    },
    {
      id: 'ingredients',
      title: 'Ingredients',
      icon: 'ShoppingCart',
      component: IngredientsSection,
      required: true,
    },
    {
      id: 'instructions',
      title: 'Instructions',
      icon: 'List',
      component: InstructionsSection,
      required: true,
    },
    {
      id: 'nutrition',
      title: 'Nutrition & Allergens',
      icon: 'Heart',
      component: NutritionSection,
      required: false,
    },
    {
      id: 'serving',
      title: 'Serving Details',
      icon: 'Users',
      component: ServingDetailsSection,
      required: true,
    },
  ];

  const handleSectionChange = (sectionId) => {
    dispatch({ type: 'SET_ACTIVE_SECTION', payload: sectionId });
  };

  const handleSaveAndExit = () => {
    // Simulate save operation
    console.error('Saving recipe...', formState);
    setShowExitModal(false);
  };

  const validateForm = () => {
    const errors = {};

    if (!formState.basicInfo.recipeName.trim()) {
      errors.recipeName = 'This field is required';
    }

    if (formState.ingredients.length === 0) {
      errors.ingredients = 'At least one ingredient is required';
    }

    if (formState.instructions.length === 0) {
      errors.instructions = 'At least one instruction step is required';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handlePreview = () => {
    if (validateForm()) {
      // Navigate to preview with form data
      // router.push({
      //   pathname: '/recipes/recipe-preview',
      //   query: { previewDetails: JSON.stringify(formState) },
      // });
      router.push('/recipes/recipe-preview');
      console.error('Navigating to preview with data:', formState);
    }
  };

  const currentSection = formSections.find(
    (section) => section.id === formState.activeSection
  );
  const CurrentSectionComponent = currentSection?.component;

  return (
    <>
      <TopBackPageButton
        title={`${isUpdate ? 'Update' : 'Add'} Recipe`}
        onBackClick={handleRedirect}
      />
      {/* Form Progress Indicator */}
      <Box className="section-right-content add-edit-recipe-container">
        <FormProgressIndicator />

        {/* Main Content */}
        <div className="add-edit-recipe__layout">
          {/* Desktop: Section Navigation Sidebar */}
          {!isMobileView && (
            <div className="add-edit-recipe__sidebar">
              <div className="add-edit-recipe__sidebar-content">
                <h2 className="add-edit-recipe__sidebar-title">
                  Recipe Sections
                </h2>

                <div className="add-edit-recipe__section-nav">
                  {formSections.map((section) => (
                    <button
                      key={section.id}
                      onClick={() => handleSectionChange(section.id)}
                      className={`add-edit-recipe__section-button ${
                        formState.activeSection === section.id
                          ? 'add-edit-recipe__section-button--active'
                          : ''
                      }`}
                    >
                      <Icon
                        name={section.icon}
                        size={20}
                        color={
                          formState.activeSection === section.id
                            ? 'white'
                            : 'currentColor'
                        }
                      />
                      <div className="add-edit-recipe__section-content">
                        <div className="add-edit-recipe__section-title">
                          {section.title}
                          {section.required && (
                            <span className="add-edit-recipe__section-required">
                              *
                            </span>
                          )}
                        </div>
                      </div>
                      {validationErrors[section.id] && (
                        <Icon name="AlertCircle" size={16} color="#EF4444" />
                      )}
                    </button>
                  ))}
                </div>

                {/* Cost Summary */}
                <div className="add-edit-recipe__cost-summary">
                  <h3 className="add-edit-recipe__cost-title">Cost Summary</h3>
                  <div className="add-edit-recipe__cost-items">
                    <div className="add-edit-recipe__cost-item">
                      <span className="add-edit-recipe__cost-label">
                        Total Cost:
                      </span>
                      <span className="add-edit-recipe__cost-value">
                        {currency}
                        {formState.calculations.totalCost.toFixed(2)}
                      </span>
                    </div>
                    <div className="add-edit-recipe__cost-item">
                      <span className="add-edit-recipe__cost-label">
                        Per Portion:
                      </span>
                      <span className="add-edit-recipe__cost-value">
                        {currency}
                        {formState.calculations.portionCost.toFixed(2)}
                      </span>
                    </div>
                    <div className="add-edit-recipe__cost-item">
                      <span className="add-edit-recipe__cost-label">
                        Total Time:
                      </span>
                      <span className="add-edit-recipe__cost-value">
                        {formState.calculations.totalTime} min
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Main Form Content */}
          <div className="add-edit-recipe__main-content">
            {/* Header Actions */}
            <div className="add-edit-recipe__header">
              <div className="add-edit-recipe__header-content">
                <div className="add-edit-recipe__header-left">
                  <h1 className="add-edit-recipe__header-title">
                    {isMobileView
                      ? currentSection?.title
                      : isUpdate
                        ? 'Update Recipe'
                        : 'Create Recipe'}
                  </h1>
                  <AutoSaveIndicator
                    status={'saved'}
                    lastSaved={'2025-06-10 08:30:00'}
                  />
                </div>

                <div className="add-edit-recipe__header-actions">
                  <CustomButton
                    onClick={handlePreview}
                    className="add-edit-recipe__preview-button"
                    variant="outlined"
                    startIcon={<Icon name="Eye" size={16} />}
                  >
                    <span className="add-edit-recipe__button-text">
                      Preview
                    </span>
                  </CustomButton>

                  <CustomButton
                    onClick={() => setShowExitModal(true)}
                    className="add-edit-recipe__save-button"
                    variant="contained"
                    startIcon={<Icon name="Save" size={16} />}
                  >
                    <span className="add-edit-recipe__button-text">
                      Save & Exit
                    </span>
                  </CustomButton>
                </div>
              </div>
            </div>

            {/* Mobile: Section Selector */}
            {isMobileView && (
              <div className="add-edit-recipe__mobile-selector">
                <div className="add-edit-recipe__mobile-nav">
                  <button
                    onClick={() => {
                      const currentIndex = formSections.findIndex(
                        (s) => s.id === formState.activeSection
                      );
                      if (currentIndex > 0) {
                        handleSectionChange(formSections[currentIndex - 1].id);
                      }
                    }}
                    disabled={
                      formSections.findIndex(
                        (s) => s.id === formState.activeSection
                      ) === 0
                    }
                    className="add-edit-recipe__mobile-nav-button"
                  >
                    <Icon name="ChevronLeft" size={16} color="currentColor" />
                    <span className="add-edit-recipe__mobile-nav-text">
                      Previous
                    </span>
                  </button>

                  <div className="add-edit-recipe__mobile-counter">
                    <span className="add-edit-recipe__mobile-counter-text">
                      {formSections.findIndex(
                        (s) => s.id === formState.activeSection
                      ) + 1}{' '}
                      of {formSections.length}
                    </span>
                  </div>

                  <button
                    onClick={() => {
                      const currentIndex = formSections.findIndex(
                        (s) => s.id === formState.activeSection
                      );
                      if (currentIndex < formSections.length - 1) {
                        handleSectionChange(formSections[currentIndex + 1].id);
                      }
                    }}
                    disabled={
                      formSections.findIndex(
                        (s) => s.id === formState.activeSection
                      ) ===
                      formSections.length - 1
                    }
                    className="add-edit-recipe__mobile-nav-button"
                  >
                    <span className="add-edit-recipe__mobile-nav-text">
                      Next
                    </span>
                    <Icon name="ChevronRight" size={16} color="currentColor" />
                  </button>
                </div>
              </div>
            )}

            {/* Form Section Content */}
            <div className="add-edit-recipe__form-content">
              {CurrentSectionComponent && (
                <CurrentSectionComponent
                  data={formState[formState.activeSection] || formState}
                  dispatch={dispatch}
                  validationErrors={validationErrors}
                  isMobileView={isMobileView}
                  currency={currency}
                />
              )}
            </div>
          </div>
        </div>
      </Box>
      <DialogBox
        open={showExitModal}
        handleClose={handleSaveAndExit}
        title="Confirmation"
        className="confirmation-modal"
        dividerClass="confirmation-modal-divider"
        content={
          <ConfirmationModal
            handleCancel={handleSaveAndExit}
            handleConfirm={handleSaveAndExit}
            text="Do you want to save your recipe before exiting? Your progress will be preserved."
            cancelText="Save & Exit"
            confirmText="Continue Editing"
          />
        }
      />
    </>
  );
}
