// Basic Info Section Component Styles
.basic-info-section {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);

  // Recipe Names Section
  &__names-section {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
  }

  &__required {
    color: var(--text-error);
  }

  &__helper-text {
    margin-top: var(--spacing-tiny);
    color: var(--text-color-slate-gray);
    font-size: var(--font-size-xxs);
    font-family: var(--font-family-primary);
  }

  // Categories and Dietary Options
  &__dropdown-section {
    position: relative;
  }

  &__dropdown-button {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--field-padding);
    border: var(--field-border);
    border-radius: var(--field-radius);
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    color: var(--field-placeholder);
    background-color: var(--color-secondary);
    cursor: pointer;
    transition: all 0.15s ease-out;

    &:hover {
      border: var(--field-border-primary);
    }
    &__selected {
      color: var(--text-color-primary);
    }
  }

  &__dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    margin-top: var(--spacing-xs);
    background-color: var(--color-white);
    border: var(--normal-sec-border);
    border-radius: var(--border-radius-xs);
    box-shadow: var(--box-shadow-xs);
    z-index: 200;
    max-height: 240px;
    overflow-y: auto;
  }

  &__dropdown-item {
    width: 100%;
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-sm) var(--spacing-md);
    text-align: left;
    background: none;
    border: none;
    cursor: pointer;
    transition: all 0.15s ease-out;
    color: var(--text-color-black);

    &:hover {
      background-color: var(--color-off-white);
    }

    &--selected {
      background-color: var(--color-primary-opacity);
      color: var(--color-primary);
    }

    &--dietary-selected {
      background-color: var(--color-success-opacity);
      color: var(--color-success);
    }
  }

  &__dropdown-item-text {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
  }

  // Selected Items Display
  &__selected-items {
    margin-top: var(--spacing-md);
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
  }

  &__selected-tag {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-tiny) var(--spacing-md);
    border-radius: var(--border-radius-full);
    font-size: var(--font-size-xxs);
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);

    &--category {
      background-color: var(--color-primary-opacity);
      color: var(--color-primary);
    }

    &--dietary {
      background-color: var(--color-success-opacity);
      color: var(--color-success);
    }
  }

  &__tag-remove {
    background: none;
    border: none;
    cursor: pointer;
    color: inherit;
    padding: 0;
    display: contents;

    &:hover {
      opacity: 0.7;
    }
  }

  // Timing Section
  &__timing-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-xxl);

    @media (min-width: 768px) {
      grid-template-columns: repeat(3, 1fr);
    }
  }

  // Visibility Section
  &__visibility-options {
    display: flex;
    gap: var(--spacing-sm);
  }

  &__visibility-button {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--btn-padding-sm);
    border-radius: var(--border-radius-md);
    border: var(--border-width-xs) solid;
    background: none;
    cursor: pointer;
    transition: all 0.15s ease-out;

    &--active {
      background-color: var(--color-primary);
      color: var(--text-color-white);
      border-color: var(--border-color-primary);
    }

    &--inactive {
      background-color: var(--color-white);
      color: var(--text-color-primary);
      border-color: var(--border-color-light-gray);

      &:hover {
        background-color: var(--color-off-white);
      }
    }
  }

  &__visibility-text {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
  }
}

// Responsive adjustments
@media (max-width: 767px) {
  .basic-info-section {
    &__visibility-options {
      flex-direction: column;
    }

    &__visibility-button {
      justify-content: center;
    }
  }
}
