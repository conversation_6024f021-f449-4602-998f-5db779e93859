import React from 'react';
import {
  Box,
  Typography,
  Select,
  MenuItem,
  Stack,
  Pagination,
  PaginationItem,
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import './custompagination.scss';

const CustomOrgPagination = ({
  currentPage,
  totalCount,
  rowsPerPage,
  onPageChange,
  className,
  OnRowPerPage,
  isInvited,
}) => {
  const totalPages = Math.ceil(totalCount / rowsPerPage);

  const pageLimitOptions = isInvited
    ? [12, 24, 50, 100, 250, 500]
    : [10, 25, 50, 100, 250, 500];

  const handlePageChange = (event, page) => {
    onPageChange(page);
  };

  return (
    <Box className={`org-custom-pagination-wrap pt24 ${className || ''}`}>
      <Box className="d-flex align-center flex-wrap gap-sm justify-space-between w100">
        {/* Rows per page */}
        <Box className="d-flex align-center gap-sm">
          <Typography variant="span" className="body-sm fw400 page-number">
            Show
          </Typography>
          <Select
            value={rowsPerPage}
            onChange={(event) => OnRowPerPage(event.target.value)}
            className="page-select caption-text fw400"
            IconComponent={ExpandMoreIcon}
            MenuProps={{
              classes: { paper: 'custom-pagination-select' },
            }}
          >
            {pageLimitOptions.map((page) => (
              <MenuItem key={page} value={page}>
                {page}
              </MenuItem>
            ))}
          </Select>
          {totalCount && (
            <Typography
              variant="span"
              className="body-sm fw400 page-number pl8"
            >
              Total: {totalCount}
            </Typography>
          )}
        </Box>

        {/* Pagination */}
        <Stack spacing={2}>
          <Pagination
            count={totalPages}
            page={currentPage}
            onChange={handlePageChange}
            renderItem={(item) => (
              <PaginationItem
                slots={{ previous: ArrowBackIcon, next: ArrowForwardIcon }}
                {...item}
              />
            )}
          />
        </Stack>
      </Box>
    </Box>
  );
};

export default CustomOrgPagination;
