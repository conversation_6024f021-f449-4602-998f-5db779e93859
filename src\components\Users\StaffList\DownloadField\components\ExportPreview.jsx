import React from 'react';
import { Box } from '@mui/material';
import '../styles/ExportPreview.scss';
import Icon from '@/components/UI/AppIcon/AppIcon';

const ExportPreview = ({ selectedFields, onClose }) => {
  // Mock sample data for preview

  const generateSampleData = (fields) => {
    const firstNames = ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'];
    const lastNames = ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'];
    const domains = [
      'gmail.com',
      'yahoo.com',
      'outlook.com',
      'company.com',
      'example.com',
    ];

    const generateRandomValue = (field, index) => {
      const firstName = firstNames[index];
      const lastName = lastNames[index];

      switch (field.type) {
        case 'text':
          if (field.id.includes('first_name')) return firstName;
          if (field.id.includes('last_name')) return lastName;
          if (field.id.includes('username'))
            return `${firstName.toLowerCase()}${lastName.toLowerCase()}${index + 1}`;
          return `Text ${index + 1}`;
        case 'email':
          return `${firstName.toLowerCase()}.${lastName.toLowerCase()}@${domains[index]}`;
        case 'number':
          if (field.id.includes('employment'))
            return `EMP${String(index + 1).padStart(4, '0')}`;
          if (field.id.includes('insurance'))
            return `INS${String(Math.floor(100000 + Math.random() * 900000))}`;
          return String(Math.floor(10000 + Math.random() * 90000));
        case 'date':
          const date = new Date();
          date.setDate(date.getDate() - index * 30); // Different dates for each record
          return date.toISOString().split('T')[0];
        case 'tel':
          return `+44 ${String(Math.floor(1000000000 + Math.random() * 9000000000))}`;
        case 'textarea':
          return `${index + 1} Business Street, City ${index + 1}, Country`;
        case 'select':
          return `Option ${index + 1}`;
        case 'boolean':
          return index % 2 === 0;
        default:
          return `Value ${index + 1}`;
      }
    };

    return Array.from({ length: 5 }, (_, index) => {
      const record = {
        sequence_number: index + 1, // Add sequence number starting from 1
        ...fields.reduce((acc, field) => {
          acc[field.id] = generateRandomValue(field, index);
          return acc;
        }, {}),
      };
      return record;
    });
  };
  const formatValue = (value, fieldType) => {
    if (!value) return '-';

    switch (fieldType) {
      case 'date':
        return new Date(value).toLocaleDateString();
      case 'datetime':
        return new Date(value).toLocaleString();
      case 'email':
        return value;
      case 'tel':
        return value;
      default:
        return value.toString();
    }
  };

  return (
    <Box className="export-preview">
      <Box className="preview-header">
        <Box className="header-content">
          <Box className="header-text">
            <h3 className="header-title">Export Preview</h3>
            <p className="header-description">
              Sample data showing how your export will look with selected fields
            </p>
          </Box>
          <button onClick={onClose} className="close-button">
            <Icon name="X" size={20} strokeWidth={2} className="close-icon" />
          </button>
        </Box>
      </Box>

      <Box className="preview-content">
        <Box className="table-container">
          <table className="preview-table">
            <thead className="table-header">
              <tr className="header-row">
                <th className="header-cell">
                  <Box className="header-content">
                    {/* <span className="order-badge">1</span> */}
                    <span className="field-name">Sequence No.</span>
                  </Box>
                </th>
                {selectedFields
                  .sort((a, b) => a.order - b.order)
                  .map((field) => (
                    <th key={field.id} className="header-cell">
                      <Box className="header-content">
                        <span className="order-badge">{field?.order}</span>
                        <span className="field-name">{field?.name}</span>
                        {field?.required && (
                          <Icon
                            name="Asterisk"
                            size={8}
                            color="#DC2626"
                            strokeWidth={3}
                            className="required-indicator"
                          />
                        )}
                      </Box>
                    </th>
                  ))}
              </tr>
            </thead>
            <tbody className="table-body">
              {selectedFields &&
                selectedFields?.length > 0 &&
                generateSampleData(selectedFields)?.map((row, rowIndex) => (
                  <tr key={rowIndex} className="data-row">
                    <td className="data-cell">{row?.sequence_number}</td>
                    {selectedFields
                      .sort((a, b) => a.order - b.order)
                      .map((field) => (
                        <td key={field.id} className="data-cell">
                          {formatValue(row[field.id], field.type)}
                        </td>
                      ))}
                  </tr>
                ))}
            </tbody>
          </table>
        </Box>

        <Box className="preview-info">
          <Box className="info-content">
            <Icon
              name="Info"
              size={16}
              color="var(--color-accent)"
              strokeWidth={2}
              className="info-icon"
            />
            <Box className="info-text">
              <p className="info-title">Preview Information</p>
              <p className="info-description">
                This preview shows sample data with your selected{' '}
                {selectedFields.length} fields in the configured sequence. The
                actual export will contain all user records matching your filter
                criteria.
              </p>
            </Box>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default ExportPreview;
