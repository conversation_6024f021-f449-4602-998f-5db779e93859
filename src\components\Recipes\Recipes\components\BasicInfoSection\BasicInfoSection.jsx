import React, { useState } from 'react';
import Icon from '@/components/UI/AppIcon/AppIcon';
import CustomTextField from '@/components/UI/CustomTextField';
// import CustomButton from '@/components/UI/CustomButton';
// import CustomSelect from '@/components/UI/CustomSelect';
import './BasicInfoSection.scss';

const BasicInfoSection = ({ data, dispatch, validationErrors }) => {
  const [showCategoryDropdown, setShowCategoryDropdown] = useState(false);
  const [showDietaryDropdown, setShowDietaryDropdown] = useState(false);

  const categoryOptions = [
    { id: 'appetizer', label: 'Appetizer', icon: 'Utensils' },
    { id: 'main-course', label: 'Main Course', icon: 'ChefHat' },
    { id: 'dessert', label: 'Dessert', icon: 'Cake' },
    { id: 'beverage', label: 'Beverage', icon: 'Coffee' },
    { id: 'salad', label: 'Salad', icon: 'Salad' },
    { id: 'soup', label: 'Soup', icon: 'Bowl' },
    { id: 'low-carb', label: 'Low Carb', icon: 'Leaf' },
    { id: 'keto', label: 'Keto', icon: 'Zap' },
    { id: 'comfort-food', label: 'Comfort Food', icon: 'Heart' },
  ];

  const dietaryOptions = [
    { id: 'vegan', label: 'Vegan', icon: 'Leaf', color: 'text-green-600' },
    {
      id: 'vegetarian',
      label: 'Vegetarian',
      icon: 'Sprout',
      color: 'text-green-500',
    },
    {
      id: 'gluten-free',
      label: 'Gluten Free',
      icon: 'Shield',
      color: 'text-blue-600',
    },
    {
      id: 'dairy-free',
      label: 'Dairy Free',
      icon: 'Milk',
      color: 'text-purple-600',
    },
    {
      id: 'nut-free',
      label: 'Nut Free',
      icon: 'AlertTriangle',
      color: 'text-orange-600',
    },
    {
      id: 'low-sodium',
      label: 'Low Sodium',
      icon: 'Droplets',
      color: 'text-cyan-600',
    },
    {
      id: 'sugar-free',
      label: 'Sugar Free',
      icon: 'Minus',
      color: 'text-red-600',
    },
    { id: 'paleo', label: 'Paleo', icon: 'Mountain', color: 'text-amber-600' },
  ];

  const handleInputChange = (field, value) => {
    dispatch({
      type: 'UPDATE_BASIC_INFO',
      payload: { [field]: value },
    });
  };

  const handleCategoryToggle = (categoryId) => {
    const currentCategories = data.basicInfo?.categories || [];
    const updatedCategories = currentCategories.includes(categoryId)
      ? currentCategories.filter((id) => id !== categoryId)
      : [...currentCategories, categoryId];

    handleInputChange('categories', updatedCategories);
  };

  const handleDietaryToggle = (dietaryId) => {
    const currentDietary = data.basicInfo?.dietaryOptions || [];
    const updatedDietary = currentDietary.includes(dietaryId)
      ? currentDietary.filter((id) => id !== dietaryId)
      : [...currentDietary, dietaryId];

    handleInputChange('dietaryOptions', updatedDietary);
  };

  // const handleVisibilityToggle = (visibilityName) => {
  //   const currentCategories = data.basicInfo?.visibility || [];
  //   const updatedCategories = currentCategories.includes(visibilityName)
  //     ? currentCategories.filter((id) => id !== visibilityName)
  //     : [...currentCategories, visibilityName];

  //   handleInputChange('visibility', updatedCategories);
  // };

  const handleVisibilityToggle = (visibilityName) => {
    const currentCategories = data.basicInfo?.visibility || [];

    const isSelected = currentCategories.includes(visibilityName);
    const updatedCategories = isSelected
      ? currentCategories.filter((item) => item !== visibilityName)
      : [...currentCategories, visibilityName];

    // Prevent visibility array from becoming empty
    if (updatedCategories.length === 0) return;

    handleInputChange('visibility', updatedCategories);
  };

  const basicInfo = data.basicInfo || {};

  return (
    <div className="basic-info-section">
      {/* Recipe Names */}
      <div className="basic-info-section__names-section">
        <div className="">
          <CustomTextField
            label="Recipe Name"
            required
            value={basicInfo.recipeName || ''}
            onChange={(e) => handleInputChange('recipeName', e.target.value)}
            placeholder="Enter the internal recipe name"
            error={!!validationErrors.recipeName}
            helperText={validationErrors.recipeName}
            fullWidth
          />
        </div>

        <div className="">
          <CustomTextField
            label="Public Display Name"
            value={basicInfo.publicDisplayName || ''}
            onChange={(e) =>
              handleInputChange('publicDisplayName', e.target.value)
            }
            error={!!validationErrors.publicDisplayName}
            placeholder="Name shown to customers (optional)"
            helperText="Leave empty to use the recipe name"
            fullWidth
          />
        </div>

        <div className="">
          <CustomTextField
            label="Description"
            value={basicInfo.recipeDecription || ''}
            onChange={(e) =>
              handleInputChange('recipeDecription', e.target.value)
            }
            error={!!validationErrors.recipeDecription}
            placeholder="Enter a description of the recipe"
            fullWidth
            multiline
            rows={3}
          />
        </div>
      </div>

      {/* Categories */}
      <div className="basic-info-section__dropdown-section">
        <p className="other-field-label">Categories</p>
        <div>
          <button
            onClick={() => setShowCategoryDropdown(!showCategoryDropdown)}
            className={`basic-info-section__dropdown-button ${basicInfo.categories.length > 0 ? 'basic-info-section__dropdown-button__selected' : ''}`}
          >
            <span>
              {basicInfo.categories?.length > 0
                ? `${basicInfo.categories.length} categories selected`
                : 'Select categories'}
            </span>
            <Icon
              name={showCategoryDropdown ? 'ChevronUp' : 'ChevronDown'}
              size={16}
              color="currentColor"
            />
          </button>

          {showCategoryDropdown && (
            <div className="basic-info-section__dropdown-menu">
              {categoryOptions.map((category) => (
                <button
                  key={category.id}
                  onClick={() => handleCategoryToggle(category.id)}
                  className={`basic-info-section__dropdown-item ${
                    basicInfo.categories?.includes(category.id)
                      ? 'basic-info-section__dropdown-item--selected'
                      : ''
                  }`}
                >
                  <Icon name={category.icon} size={16} color="currentColor" />
                  <span className="basic-info-section__dropdown-item-text">
                    {category.label}
                  </span>
                  {basicInfo.categories?.includes(category.id) && (
                    <Icon
                      name="Check"
                      size={16}
                      color="currentColor"
                      style={{ marginLeft: 'auto' }}
                    />
                  )}
                </button>
              ))}
            </div>
          )}
        </div>

        {/* Selected Categories Display */}
        {basicInfo.categories?.length > 0 && (
          <div className="basic-info-section__selected-items">
            {basicInfo.categories.map((categoryId) => {
              const category = categoryOptions.find((c) => c.id === categoryId);
              return (
                <span
                  key={categoryId}
                  className="basic-info-section__selected-tag basic-info-section__selected-tag--category"
                >
                  <Icon name={category?.icon} size={12} color="currentColor" />
                  <span>{category?.label}</span>
                  <button
                    onClick={() => handleCategoryToggle(categoryId)}
                    className="basic-info-section__tag-remove"
                  >
                    <Icon name="X" size={12} color="currentColor" />
                  </button>
                </span>
              );
            })}
          </div>
        )}
      </div>

      {/* Dietary Options */}
      <div className="basic-info-section__dropdown-section">
        <p className="other-field-label">Dietary Suitability</p>
        <div>
          <button
            onClick={() => setShowDietaryDropdown(!showDietaryDropdown)}
            className={`basic-info-section__dropdown-button ${basicInfo.dietaryOptions.length > 0 ? 'basic-info-section__dropdown-button__selected' : ''}`}
          >
            <span>
              {basicInfo.dietaryOptions?.length > 0
                ? `${basicInfo.dietaryOptions.length} dietary options selected`
                : 'Select dietary options'}
            </span>
            <Icon
              name={showDietaryDropdown ? 'ChevronUp' : 'ChevronDown'}
              size={16}
              color="currentColor"
            />
          </button>

          {showDietaryDropdown && (
            <div className="basic-info-section__dropdown-menu">
              {dietaryOptions.map((option) => (
                <button
                  key={option.id}
                  onClick={() => handleDietaryToggle(option.id)}
                  className={`basic-info-section__dropdown-item ${
                    basicInfo.dietaryOptions?.includes(option.id)
                      ? 'basic-info-section__dropdown-item--dietary-selected'
                      : ''
                  }`}
                >
                  <Icon name={option.icon} size={16} color="currentColor" />
                  <span className="basic-info-section__dropdown-item-text">
                    {option.label}
                  </span>
                  {basicInfo.dietaryOptions?.includes(option.id) && (
                    <Icon
                      name="Check"
                      size={16}
                      color="currentColor"
                      style={{ marginLeft: 'auto' }}
                    />
                  )}
                </button>
              ))}
            </div>
          )}
        </div>

        {/* Selected Dietary Options Display */}
        {basicInfo.dietaryOptions?.length > 0 && (
          <div className="basic-info-section__selected-items">
            {basicInfo.dietaryOptions.map((optionId) => {
              const option = dietaryOptions.find((o) => o.id === optionId);
              return (
                <span
                  key={optionId}
                  className="basic-info-section__selected-tag basic-info-section__selected-tag--dietary"
                >
                  <Icon name={option?.icon} size={12} color="currentColor" />
                  <span>{option?.label}</span>
                  <button
                    onClick={() => handleDietaryToggle(optionId)}
                    className="basic-info-section__tag-remove"
                  >
                    <Icon name="X" size={12} color="currentColor" />
                  </button>
                </span>
              );
            })}
          </div>
        )}
      </div>

      {/* Timing */}
      <div className="basic-info-section__timing-grid">
        <div className="">
          <CustomTextField
            label="Prep Time (minutes)"
            type="number"
            value={basicInfo.prepTime || ''}
            onChange={(e) =>
              handleInputChange('prepTime', parseInt(e.target.value) || 0)
            }
            placeholder="0"
            inputProps={{ min: 0 }}
            fullWidth
          />
        </div>

        <div className="">
          <CustomTextField
            label="Cook Time (minutes)"
            type="number"
            value={basicInfo.cookTime || ''}
            onChange={(e) =>
              handleInputChange('cookTime', parseInt(e.target.value) || 0)
            }
            placeholder="0"
            inputProps={{ min: 0 }}
            fullWidth
          />
        </div>

        <div className="">
          <CustomTextField
            label="Total Time"
            value={
              (basicInfo.prepTime || 0) +
                (basicInfo.cookTime || 0) +
                ' minutes' || ''
            }
            onChange={(e) =>
              handleInputChange('cookTime', parseInt(e.target.value) || 0)
            }
            placeholder="0 minutes"
            fullWidth
            disabled
          />
        </div>
      </div>

      {/* Visibility */}
      <div>
        <p className="other-field-label">Recipe Visibility</p>
        <div className="basic-info-section__visibility-options mt8">
          <button
            onClick={() => handleVisibilityToggle('private')}
            className={`basic-info-section__visibility-button ${
              basicInfo.visibility?.includes('private')
                ? 'basic-info-section__visibility-button--active'
                : 'basic-info-section__visibility-button--inactive'
            }`}
          >
            <Icon name="Lock" size={16} color="currentColor" />
            <span className="basic-info-section__visibility-text">Private</span>
          </button>
          <button
            onClick={() => handleVisibilityToggle('public')}
            className={`basic-info-section__visibility-button ${
              basicInfo.visibility?.includes('public')
                ? 'basic-info-section__visibility-button--active'
                : 'basic-info-section__visibility-button--inactive'
            }`}
          >
            <Icon name="Globe" size={16} color="currentColor" />
            <span className="basic-info-section__visibility-text">Public</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default BasicInfoSection;
